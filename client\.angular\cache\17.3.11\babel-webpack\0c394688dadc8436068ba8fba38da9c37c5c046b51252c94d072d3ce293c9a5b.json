{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../activities.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"primeng/dialog\";\nimport * as i9 from \"primeng/multiselect\";\nconst _c0 = () => ({\n  width: \"45rem\"\n});\nfunction SalesCallAttachmentsComponent_ng_template_10_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_10_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 38);\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_10_ng_container_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.sortOrder === 1 ? \"pi-sort-amount-up-alt\" : \"pi-sort-amount-down\");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_10_ng_container_6_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 38);\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_10_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 39);\n    i0.ɵɵlistener(\"click\", function SalesCallAttachmentsComponent_ng_template_10_ng_container_6_Template_th_click_1_listener() {\n      const col_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.customSort(col_r5.field));\n    });\n    i0.ɵɵelementStart(2, \"div\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, SalesCallAttachmentsComponent_ng_template_10_ng_container_6_i_4_Template, 1, 1, \"i\", 33)(5, SalesCallAttachmentsComponent_ng_template_10_ng_container_6_i_5_Template, 1, 0, \"i\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pSortableColumn\", col_r5.field);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", col_r5.header, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === col_r5.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== col_r5.field);\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 31);\n    i0.ɵɵlistener(\"click\", function SalesCallAttachmentsComponent_ng_template_10_Template_th_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.customSort(\"name\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 32);\n    i0.ɵɵtext(3, \" File Name \");\n    i0.ɵɵtemplate(4, SalesCallAttachmentsComponent_ng_template_10_i_4_Template, 1, 1, \"i\", 33)(5, SalesCallAttachmentsComponent_ng_template_10_i_5_Template, 1, 0, \"i\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, SalesCallAttachmentsComponent_ng_template_10_ng_container_6_Template, 6, 4, \"ng-container\", 35);\n    i0.ɵɵelementStart(7, \"th\", 36);\n    i0.ɵɵtext(8, \"Action\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField === \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.sortField !== \"name\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_11_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"href\", attachment_r6.link_web_uri, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", attachment_r6.name || \"Attachment\", \" \");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_11_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", attachment_r6.name || \"Attachment\", \" \");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_11_ng_container_7_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const attachment_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", attachment_r6.mime_type || \"-\", \" \");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_11_ng_container_7_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const attachment_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, attachment_r6.createdAt, \"short\"), \" \");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_11_ng_container_7_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const attachment_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, attachment_r6.updatedAt, \"short\"), \" \");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_11_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementContainerStart(2, 48);\n    i0.ɵɵtemplate(3, SalesCallAttachmentsComponent_ng_template_11_ng_container_7_ng_container_3_Template, 2, 1, \"ng-container\", 49)(4, SalesCallAttachmentsComponent_ng_template_11_ng_container_7_ng_container_4_Template, 3, 4, \"ng-container\", 49)(5, SalesCallAttachmentsComponent_ng_template_11_ng_container_7_ng_container_5_Template, 3, 4, \"ng-container\", 49);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const col_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", col_r7.field);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"mime_type\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"createdAt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"updatedAt\");\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_11_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function SalesCallAttachmentsComponent_ng_template_11_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const attachment_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.downloadAttachment(attachment_r6));\n    });\n    i0.ɵɵelementStart(1, \"i\", 43);\n    i0.ɵɵtext(2, \"download\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 40)(1, \"td\", 41)(2, \"div\", 42)(3, \"i\", 43);\n    i0.ɵɵtext(4, \"attach_file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SalesCallAttachmentsComponent_ng_template_11_a_5_Template, 2, 2, \"a\", 44)(6, SalesCallAttachmentsComponent_ng_template_11_span_6_Template, 2, 1, \"span\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, SalesCallAttachmentsComponent_ng_template_11_ng_container_7_Template, 6, 4, \"ng-container\", 35);\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtemplate(9, SalesCallAttachmentsComponent_ng_template_11_button_9_Template, 3, 0, \"button\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const attachment_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", attachment_r6.link_web_uri);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !attachment_r6.link_web_uri);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedColumns);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", attachment_r6.link_web_uri);\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 51);\n    i0.ɵɵtext(2, \"No attachments found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 51);\n    i0.ɵɵtext(2, \"Loading attachments data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SalesCallAttachmentsComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1, \"Add Attachment\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallAttachmentsComponent_small_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 52);\n    i0.ɵɵtext(1, \" File name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallAttachmentsComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"i\", 54);\n    i0.ɵɵtext(2, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 55);\n    i0.ɵɵtext(4, \"Click to select a file or drag and drop\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"label\", 56);\n    i0.ɵɵelement(6, \"i\", 57);\n    i0.ɵɵtext(7, \" Choose File \");\n    i0.ɵɵelementStart(8, \"input\", 58);\n    i0.ɵɵlistener(\"change\", function SalesCallAttachmentsComponent_div_29_Template_input_change_8_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFileSelect($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SalesCallAttachmentsComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 42)(2, \"i\", 43);\n    i0.ɵɵtext(3, \"attach_file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\")(5, \"p\", 60);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"small\", 61);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function SalesCallAttachmentsComponent_div_30_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeSelectedFile());\n    });\n    i0.ɵɵelement(10, \"i\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedFile.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatFileSize(ctx_r2.selectedFile.size));\n  }\n}\nfunction SalesCallAttachmentsComponent_small_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 52);\n    i0.ɵɵtext(1, \" Please select a file \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SalesCallAttachmentsComponent_i_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 64);\n  }\n}\nexport class SalesCallAttachmentsComponent {\n  constructor(activitiesservice, formBuilder, messageService) {\n    this.activitiesservice = activitiesservice;\n    this.formBuilder = formBuilder;\n    this.messageService = messageService;\n    this.unsubscribe$ = new Subject();\n    this.attachmentdetails = [];\n    this.bp_id = '';\n    this.activity_id = '';\n    this.loading = false;\n    // Modal properties\n    this.attachmentDialogVisible = false;\n    this.selectedFile = null;\n    this.uploading = false;\n    this.attachmentFormSubmitted = false;\n    this._selectedColumns = [];\n    this.cols = [{\n      field: 'mime_type',\n      header: 'Type'\n    }, {\n      field: 'createdAt',\n      header: 'Created On'\n    }, {\n      field: 'updatedAt',\n      header: 'Updated On'\n    }];\n    this.sortField = '';\n    this.sortOrder = 1;\n    this.attachmentForm = this.formBuilder.group({\n      name: ['', Validators.required],\n      mime_type: ['']\n    });\n  }\n  customSort(field) {\n    if (this.sortField === field) {\n      this.sortOrder = -this.sortOrder;\n    } else {\n      this.sortField = field;\n      this.sortOrder = 1;\n    }\n    this.attachmentdetails.sort((a, b) => {\n      const value1 = this.resolveFieldData(a, field);\n      const value2 = this.resolveFieldData(b, field);\n      let result = 0;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n      return this.sortOrder * result;\n    });\n  }\n  // Utility to resolve nested fields\n  resolveFieldData(data, field) {\n    if (!data || !field) return null;\n    if (field.indexOf('.') === -1) {\n      return data[field];\n    } else {\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\n    }\n  }\n  ngOnInit() {\n    this.activitiesservice.activity.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      if (response) {\n        this.bp_id = response?.bp_id;\n        this.activity_id = response?.activity_id;\n        if (this.activity_id) {\n          this.loadAttachments();\n        }\n      }\n    });\n    this._selectedColumns = this.cols;\n  }\n  loadAttachments() {\n    this.loading = true;\n    this.activitiesservice.getCrmAttachments(this.activity_id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.attachmentdetails = response?.data || [];\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading attachments:', error);\n        this.attachmentdetails = [];\n        this.loading = false;\n      }\n    });\n  }\n  downloadAttachment(attachment) {\n    if (attachment.link_web_uri) {\n      window.open(attachment.link_web_uri, '_blank');\n    }\n  }\n  get selectedColumns() {\n    return this._selectedColumns;\n  }\n  set selectedColumns(val) {\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\n  }\n  onColumnReorder(event) {\n    const draggedCol = this._selectedColumns[event.dragIndex];\n    this._selectedColumns.splice(event.dragIndex, 1);\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function SalesCallAttachmentsComponent_Factory(t) {\n      return new (t || SalesCallAttachmentsComponent)(i0.ɵɵdirectiveInject(i1.ActivitiesService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SalesCallAttachmentsComponent,\n      selectors: [[\"app-sales-call-attachments\"]],\n      decls: 42,\n      vars: 26,\n      consts: [[\"dt1\", \"\"], [1, \"p-3\", \"w-full\", \"surface-card\", \"border-round\", \"shadow-1\"], [1, \"card-heading\", \"filter-sec\", \"mb-5\", \"flex\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [1, \"m-0\", \"pl-3\", \"left-border\", \"relative\", \"flex\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [\"label\", \"Add\", \"icon\", \"pi pi-plus-circle\", \"iconPos\", \"right\", 1, \"ml-auto\", 3, \"click\", \"styleClass\", \"rounded\"], [\"optionLabel\", \"header\", 1, \"table-multiselect-dropdown\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"styleClass\"], [1, \"table-sec\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 1, \"scrollable-table\", 3, \"onColReorder\", \"value\", \"rows\", \"paginator\", \"loading\", \"scrollable\", \"reorderableColumns\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"attachment-popup\", 3, \"visibleChange\", \"modal\", \"visible\", \"position\", \"draggable\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"grid\"], [1, \"col-12\"], [1, \"field\"], [\"for\", \"attachmentName\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"pInputText\", \"\", \"id\", \"attachmentName\", \"formControlName\", \"name\", \"placeholder\", \"Enter file name\", 1, \"w-full\"], [\"class\", \"p-error\", 4, \"ngIf\"], [1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [1, \"file-upload-container\", \"border-1\", \"border-round\", \"border-dashed\", \"border-300\", \"p-4\", \"text-center\"], [\"class\", \"flex flex-column align-items-center gap-3\", 4, \"ngIf\"], [\"class\", \"flex align-items-center justify-content-between p-3 bg-primary-50 border-round\", 4, \"ngIf\"], [\"for\", \"mimeType\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"pInputText\", \"\", \"id\", \"mimeType\", \"formControlName\", \"mime_type\", \"placeholder\", \"Auto-detected from file\", \"readonly\", \"\", 1, \"w-full\"], [1, \"flex\", \"justify-content-end\", \"gap-2\", \"mt-4\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Cancel\", 1, \"p-button-outlined\", \"p-button-rounded\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"submit\", \"label\", \"Attach File\", 1, \"p-button-rounded\", 3, \"disabled\"], [\"class\", \"pi pi-spin pi-spinner mr-2\", 4, \"ngIf\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"cursor-pointer\"], [\"class\", \"ml-2 pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"ml-2 pi pi-sort\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"border-round-right-lg\"], [1, \"ml-2\", \"pi\", 3, \"ngClass\"], [1, \"ml-2\", \"pi\", \"pi-sort\"], [\"pReorderableColumn\", \"\", 3, \"click\", \"pSortableColumn\"], [1, \"cursor-pointer\"], [\"pFrozenColumn\", \"\", 1, \"border-round-left-lg\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\", \"text-primary\"], [\"target\", \"_blank\", \"class\", \"text-primary underline\", 3, \"href\", 4, \"ngIf\"], [4, \"ngIf\"], [\"type\", \"button\", \"class\", \"p-element p-button p-component p-button-icon-only surface-0 h-2rem w-2rem border-none\", 3, \"click\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"text-primary\", \"underline\", 3, \"href\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"type\", \"button\", 1, \"p-element\", \"p-button\", \"p-component\", \"p-button-icon-only\", \"surface-0\", \"h-2rem\", \"w-2rem\", \"border-none\", 3, \"click\"], [\"colspan\", \"6\", 1, \"border-round-left-lg\"], [1, \"p-error\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"gap-3\"], [1, \"material-symbols-rounded\", \"text-primary\", \"text-4xl\"], [1, \"m-0\", \"text-600\"], [\"for\", \"fileInput\", 1, \"p-button\", \"p-button-outlined\", \"p-button-rounded\", \"cursor-pointer\"], [1, \"pi\", \"pi-plus\", \"mr-2\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"*/*\", 2, \"display\", \"none\", 3, \"change\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"p-3\", \"bg-primary-50\", \"border-round\"], [1, \"m-0\", \"font-medium\"], [1, \"text-600\"], [\"type\", \"button\", 1, \"p-button\", \"p-button-text\", \"p-button-rounded\", \"p-button-danger\", 3, \"click\"], [1, \"pi\", \"pi-times\"], [1, \"pi\", \"pi-spin\", \"pi-spinner\", \"mr-2\"]],\n      template: function SalesCallAttachmentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h4\", 3);\n          i0.ɵɵtext(3, \"Attachments\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"p-button\", 5);\n          i0.ɵɵlistener(\"click\", function SalesCallAttachmentsComponent_Template_p_button_click_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.showAddAttachmentDialog());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p-multiSelect\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesCallAttachmentsComponent_Template_p_multiSelect_ngModelChange_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedColumns, $event) || (ctx.selectedColumns = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"p-table\", 8, 0);\n          i0.ɵɵlistener(\"onColReorder\", function SalesCallAttachmentsComponent_Template_p_table_onColReorder_8_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onColumnReorder($event));\n          });\n          i0.ɵɵtemplate(10, SalesCallAttachmentsComponent_ng_template_10_Template, 9, 3, \"ng-template\", 9)(11, SalesCallAttachmentsComponent_ng_template_11_Template, 10, 4, \"ng-template\", 10)(12, SalesCallAttachmentsComponent_ng_template_12_Template, 3, 0, \"ng-template\", 11)(13, SalesCallAttachmentsComponent_ng_template_13_Template, 3, 0, \"ng-template\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"p-dialog\", 13);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function SalesCallAttachmentsComponent_Template_p_dialog_visibleChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.attachmentDialogVisible, $event) || (ctx.attachmentDialogVisible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(15, SalesCallAttachmentsComponent_ng_template_15_Template, 2, 0, \"ng-template\", 9);\n          i0.ɵɵelementStart(16, \"form\", 14);\n          i0.ɵɵlistener(\"ngSubmit\", function SalesCallAttachmentsComponent_Template_form_ngSubmit_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSubmitAttachment());\n          });\n          i0.ɵɵelementStart(17, \"div\", 15)(18, \"div\", 16)(19, \"div\", 17)(20, \"label\", 18);\n          i0.ɵɵtext(21, \"File Name *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 19);\n          i0.ɵɵtemplate(23, SalesCallAttachmentsComponent_small_23_Template, 2, 0, \"small\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 16)(25, \"div\", 17)(26, \"label\", 21);\n          i0.ɵɵtext(27, \"Select File *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 22);\n          i0.ɵɵtemplate(29, SalesCallAttachmentsComponent_div_29_Template, 9, 0, \"div\", 23)(30, SalesCallAttachmentsComponent_div_30_Template, 11, 2, \"div\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, SalesCallAttachmentsComponent_small_31_Template, 2, 0, \"small\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 16)(33, \"div\", 17)(34, \"label\", 25);\n          i0.ɵɵtext(35, \"MIME Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(36, \"input\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"div\", 27)(38, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function SalesCallAttachmentsComponent_Template_button_click_38_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.hideAttachmentDialog());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"button\", 29);\n          i0.ɵɵtemplate(40, SalesCallAttachmentsComponent_i_40_Template, 1, 0, \"i\", 30);\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_18_0;\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"styleClass\", \"font-semibold px-3\")(\"rounded\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.cols);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedColumns);\n          i0.ɵɵproperty(\"styleClass\", \"relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.attachmentdetails)(\"rows\", 8)(\"paginator\", true)(\"loading\", ctx.loading)(\"scrollable\", true)(\"reorderableColumns\", true);\n          i0.ɵɵadvance(6);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(25, _c0));\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.attachmentDialogVisible);\n          i0.ɵɵproperty(\"position\", \"right\")(\"draggable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.attachmentForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx.attachmentForm.get(\"name\")) == null ? null : tmp_18_0.invalid) && ((tmp_18_0 = ctx.attachmentForm.get(\"name\")) == null ? null : tmp_18_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", !ctx.selectedFile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedFile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.selectedFile && ctx.attachmentFormSubmitted);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedFile || ctx.attachmentForm.invalid || ctx.uploading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.uploading);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.uploading ? \"Uploading...\" : \"Attach File\", \" \");\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgSwitch, i4.NgSwitchCase, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.FormGroupDirective, i2.FormControlName, i5.Table, i3.PrimeTemplate, i5.SortableColumn, i5.FrozenColumn, i5.ReorderableColumn, i6.ButtonDirective, i6.Button, i7.InputText, i8.Dialog, i9.MultiSelect, i4.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r2", "sortOrder", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "SalesCallAttachmentsComponent_ng_template_10_ng_container_6_Template_th_click_1_listener", "col_r5", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "customSort", "field", "ɵɵtext", "ɵɵtemplate", "SalesCallAttachmentsComponent_ng_template_10_ng_container_6_i_4_Template", "SalesCallAttachmentsComponent_ng_template_10_ng_container_6_i_5_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "header", "sortField", "SalesCallAttachmentsComponent_ng_template_10_Template_th_click_1_listener", "_r2", "SalesCallAttachmentsComponent_ng_template_10_i_4_Template", "SalesCallAttachmentsComponent_ng_template_10_i_5_Template", "SalesCallAttachmentsComponent_ng_template_10_ng_container_6_Template", "selectedColumns", "attachment_r6", "link_web_uri", "ɵɵsanitizeUrl", "name", "mime_type", "ɵɵpipeBind2", "createdAt", "updatedAt", "SalesCallAttachmentsComponent_ng_template_11_ng_container_7_ng_container_3_Template", "SalesCallAttachmentsComponent_ng_template_11_ng_container_7_ng_container_4_Template", "SalesCallAttachmentsComponent_ng_template_11_ng_container_7_ng_container_5_Template", "col_r7", "SalesCallAttachmentsComponent_ng_template_11_button_9_Template_button_click_0_listener", "_r8", "downloadAttachment", "SalesCallAttachmentsComponent_ng_template_11_a_5_Template", "SalesCallAttachmentsComponent_ng_template_11_span_6_Template", "SalesCallAttachmentsComponent_ng_template_11_ng_container_7_Template", "SalesCallAttachmentsComponent_ng_template_11_button_9_Template", "SalesCallAttachmentsComponent_div_29_Template_input_change_8_listener", "$event", "_r9", "onFileSelect", "SalesCallAttachmentsComponent_div_30_Template_button_click_9_listener", "_r10", "removeSelectedFile", "ɵɵtextInterpolate", "selectedFile", "formatFileSize", "size", "SalesCallAttachmentsComponent", "constructor", "activitiesservice", "formBuilder", "messageService", "unsubscribe$", "attachmentdetails", "bp_id", "activity_id", "loading", "attachmentDialogVisible", "uploading", "attachmentFormSubmitted", "_selectedColumns", "cols", "attachmentForm", "group", "required", "sort", "a", "b", "value1", "resolveFieldData", "value2", "result", "localeCompare", "data", "indexOf", "split", "reduce", "obj", "key", "ngOnInit", "activity", "pipe", "subscribe", "response", "loadAttachments", "getCrmAttachments", "next", "error", "console", "attachment", "window", "open", "val", "filter", "col", "includes", "onColumnReorder", "event", "draggedCol", "dragIndex", "splice", "dropIndex", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivitiesService", "i2", "FormBuilder", "i3", "MessageService", "selectors", "decls", "vars", "consts", "template", "SalesCallAttachmentsComponent_Template", "rf", "ctx", "SalesCallAttachmentsComponent_Template_p_button_click_5_listener", "_r1", "showAddAttachmentDialog", "ɵɵtwoWayListener", "SalesCallAttachmentsComponent_Template_p_multiSelect_ngModelChange_6_listener", "ɵɵtwoWayBindingSet", "SalesCallAttachmentsComponent_Template_p_table_onColReorder_8_listener", "SalesCallAttachmentsComponent_ng_template_10_Template", "SalesCallAttachmentsComponent_ng_template_11_Template", "SalesCallAttachmentsComponent_ng_template_12_Template", "SalesCallAttachmentsComponent_ng_template_13_Template", "SalesCallAttachmentsComponent_Template_p_dialog_visibleChange_14_listener", "SalesCallAttachmentsComponent_ng_template_15_Template", "SalesCallAttachmentsComponent_Template_form_ngSubmit_16_listener", "onSubmitAttachment", "SalesCallAttachmentsComponent_small_23_Template", "SalesCallAttachmentsComponent_div_29_Template", "SalesCallAttachmentsComponent_div_30_Template", "SalesCallAttachmentsComponent_small_31_Template", "SalesCallAttachmentsComponent_Template_button_click_38_listener", "hideAttachmentDialog", "SalesCallAttachmentsComponent_i_40_Template", "ɵɵtwoWayProperty", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "tmp_18_0", "get", "invalid", "touched"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-attachments\\sales-call-attachments.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\sales-call\\sales-call-details\\sales-call-attachments\\sales-call-attachments.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { MessageService } from 'primeng/api';\r\nimport { ActivitiesService } from '../../../activities.service';\r\n\r\ninterface Column {\r\n  field: string;\r\n  header: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-sales-call-attachments',\r\n  templateUrl: './sales-call-attachments.component.html',\r\n  styleUrl: './sales-call-attachments.component.scss',\r\n})\r\nexport class SalesCallAttachmentsComponent implements OnInit, OnDestroy {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public attachmentdetails: any[] = [];\r\n  public bp_id: string = '';\r\n  public activity_id: string = '';\r\n  public loading: boolean = false;\r\n\r\n  // Modal properties\r\n  public attachmentDialogVisible: boolean = false;\r\n  public attachmentForm: FormGroup;\r\n  public selectedFile: File | null = null;\r\n  public uploading: boolean = false;\r\n  public attachmentFormSubmitted: boolean = false;\r\n\r\n  constructor(\r\n    private activitiesservice: ActivitiesService,\r\n    private formBuilder: FormBuilder,\r\n    private messageService: MessageService\r\n  ) {\r\n    this.attachmentForm = this.formBuilder.group({\r\n      name: ['', Validators.required],\r\n      mime_type: ['']\r\n    });\r\n  }\r\n\r\n  private _selectedColumns: Column[] = [];\r\n\r\n  public cols: Column[] = [\r\n    { field: 'mime_type', header: 'Type' },\r\n    { field: 'createdAt', header: 'Created On' },\r\n    { field: 'updatedAt', header: 'Updated On' },\r\n  ];\r\n\r\n  sortField: string = '';\r\n  sortOrder: number = 1;\r\n\r\n  customSort(field: string): void {\r\n    if (this.sortField === field) {\r\n      this.sortOrder = -this.sortOrder;\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortOrder = 1;\r\n    }\r\n\r\n    this.attachmentdetails.sort((a, b) => {\r\n      const value1 = this.resolveFieldData(a, field);\r\n      const value2 = this.resolveFieldData(b, field);\r\n\r\n      let result = 0;\r\n\r\n      if (value1 == null && value2 != null) result = -1;\r\n      else if (value1 != null && value2 == null) result = 1;\r\n      else if (value1 == null && value2 == null) result = 0;\r\n      else if (typeof value1 === 'string' && typeof value2 === 'string')\r\n        result = value1.localeCompare(value2);\r\n      else result = (value1 < value2 ? -1 : (value1 > value2 ? 1 : 0));\r\n\r\n      return this.sortOrder * result;\r\n    });\r\n  }\r\n\r\n  // Utility to resolve nested fields\r\n  resolveFieldData(data: any, field: string): any {\r\n    if (!data || !field) return null;\r\n    if (field.indexOf('.') === -1) {\r\n      return data[field];\r\n    } else {\r\n      return field.split('.').reduce((obj, key) => obj?.[key], data);\r\n    }\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.activitiesservice.activity\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.bp_id = response?.bp_id;\r\n          this.activity_id = response?.activity_id;\r\n\r\n          if (this.activity_id) {\r\n            this.loadAttachments();\r\n          }\r\n        }\r\n      });\r\n\r\n    this._selectedColumns = this.cols;\r\n  }\r\n\r\n  private loadAttachments(): void {\r\n    this.loading = true;\r\n    this.activitiesservice.getCrmAttachments(this.activity_id)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.attachmentdetails = response?.data || [];\r\n          this.loading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading attachments:', error);\r\n          this.attachmentdetails = [];\r\n          this.loading = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  downloadAttachment(attachment: any): void {\r\n    if (attachment.link_web_uri) {\r\n      window.open(attachment.link_web_uri, '_blank');\r\n    }\r\n  }\r\n\r\n  get selectedColumns(): any[] {\r\n    return this._selectedColumns;\r\n  }\r\n\r\n  set selectedColumns(val: any[]) {\r\n    this._selectedColumns = this.cols.filter(col => val.includes(col));\r\n  }\r\n\r\n  onColumnReorder(event: any) {\r\n    const draggedCol = this._selectedColumns[event.dragIndex];\r\n    this._selectedColumns.splice(event.dragIndex, 1);\r\n    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);\r\n  }\r\n\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"p-3 w-full surface-card border-round shadow-1\">\r\n    <div class=\"card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2\">\r\n        <h4 class=\"m-0 pl-3 left-border relative flex\">Attachments</h4>\r\n\r\n        <div class=\"flex align-items-center gap-3\">\r\n            <p-button label=\"Add\" icon=\"pi pi-plus-circle\" iconPos=\"right\" class=\"ml-auto\"\r\n                [styleClass]=\"'font-semibold px-3'\" [rounded]=\"true\" (click)=\"showAddAttachmentDialog()\" />\r\n\r\n            <p-multiSelect [options]=\"cols\" [(ngModel)]=\"selectedColumns\" optionLabel=\"header\"\r\n                class=\"table-multiselect-dropdown\"\r\n                [styleClass]=\"'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'\">\r\n            </p-multiSelect>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"table-sec\">\r\n\r\n        <p-table #dt1 [value]=\"attachmentdetails\" dataKey=\"id\" [rows]=\"8\" [paginator]=\"true\" [loading]=\"loading\"\r\n            responsiveLayout=\"scroll\" [scrollable]=\"true\" class=\"scrollable-table\" [reorderableColumns]=\"true\"\r\n            (onColReorder)=\"onColumnReorder($event)\">\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th pFrozenColumn (click)=\"customSort('name')\" class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center cursor-pointer\">\r\n                            File Name\r\n                            <i *ngIf=\"sortField === 'name'\" class=\"ml-2 pi\"\r\n                                [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                            </i>\r\n                            <i *ngIf=\"sortField !== 'name'\" class=\"ml-2 pi pi-sort\"></i>\r\n                        </div>\r\n                    </th>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <th [pSortableColumn]=\"col.field\" pReorderableColumn (click)=\"customSort(col.field)\">\r\n                            <div class=\"flex align-items-center cursor-pointer\">\r\n                                {{ col.header }}\r\n                                <i *ngIf=\"sortField === col.field\" class=\"ml-2 pi\"\r\n                                    [ngClass]=\"sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'\">\r\n                                </i>\r\n                                <i *ngIf=\"sortField !== col.field\" class=\"ml-2 pi pi-sort\"></i>\r\n                            </div>\r\n                        </th>\r\n                    </ng-container>\r\n                    <th class=\"border-round-right-lg\">Action</th>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"body\" let-attachment let-columns=\"columns\">\r\n                <tr class=\"cursor-pointer\">\r\n                    <td pFrozenColumn class=\"border-round-left-lg\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <i class=\"material-symbols-rounded text-primary\">attach_file</i>\r\n                            <a *ngIf=\"attachment.link_web_uri\" [href]=\"attachment.link_web_uri\" target=\"_blank\"\r\n                               class=\"text-primary underline\">\r\n                                {{ attachment.name || 'Attachment' }}\r\n                            </a>\r\n                            <span *ngIf=\"!attachment.link_web_uri\">\r\n                                {{ attachment.name || 'Attachment' }}\r\n                            </span>\r\n                        </div>\r\n                    </td>\r\n                    <ng-container *ngFor=\"let col of selectedColumns\">\r\n                        <td>\r\n                            <ng-container [ngSwitch]=\"col.field\">\r\n                                <ng-container *ngSwitchCase=\"'mime_type'\">\r\n                                    {{ attachment.mime_type || '-' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'createdAt'\">\r\n                                    {{ attachment.createdAt | date:'short' }}\r\n                                </ng-container>\r\n\r\n                                <ng-container *ngSwitchCase=\"'updatedAt'\">\r\n                                    {{ attachment.updatedAt | date:'short' }}\r\n                                </ng-container>\r\n\r\n                            </ng-container>\r\n                        </td>\r\n                    </ng-container>\r\n                    <td>\r\n                        <button type=\"button\" *ngIf=\"attachment.link_web_uri\"\r\n                            class=\"p-element p-button p-component p-button-icon-only surface-0 h-2rem w-2rem border-none\"\r\n                            (click)=\"downloadAttachment(attachment)\">\r\n                            <i class=\"material-symbols-rounded text-primary\">download</i>\r\n                        </button>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"6\" class=\"border-round-left-lg\">No attachments found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"6\" class=\"border-round-left-lg\">Loading attachments data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>\r\n\r\n<!-- Add Attachment Modal -->\r\n<p-dialog [modal]=\"true\" [(visible)]=\"attachmentDialogVisible\" [style]=\"{ width: '45rem' }\" [position]=\"'right'\"\r\n    [draggable]=\"false\" class=\"attachment-popup\">\r\n    <ng-template pTemplate=\"header\">\r\n        <h4>Add Attachment</h4>\r\n    </ng-template>\r\n\r\n    <form [formGroup]=\"attachmentForm\" (ngSubmit)=\"onSubmitAttachment()\">\r\n        <div class=\"grid\">\r\n            <div class=\"col-12\">\r\n                <div class=\"field\">\r\n                    <label for=\"attachmentName\" class=\"block text-900 font-medium mb-2\">File Name *</label>\r\n                    <input pInputText id=\"attachmentName\" formControlName=\"name\"\r\n                           class=\"w-full\" placeholder=\"Enter file name\" />\r\n                    <small class=\"p-error\" *ngIf=\"attachmentForm.get('name')?.invalid && attachmentForm.get('name')?.touched\">\r\n                        File name is required\r\n                    </small>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12\">\r\n                <div class=\"field\">\r\n                    <label class=\"block text-900 font-medium mb-2\">Select File *</label>\r\n                    <div class=\"file-upload-container border-1 border-round border-dashed border-300 p-4 text-center\">\r\n                        <div *ngIf=\"!selectedFile\" class=\"flex flex-column align-items-center gap-3\">\r\n                            <i class=\"material-symbols-rounded text-primary text-4xl\">cloud_upload</i>\r\n                            <p class=\"m-0 text-600\">Click to select a file or drag and drop</p>\r\n                            <label for=\"fileInput\" class=\"p-button p-button-outlined p-button-rounded cursor-pointer\">\r\n                                <i class=\"pi pi-plus mr-2\"></i>\r\n                                Choose File\r\n                                <input type=\"file\" id=\"fileInput\" (change)=\"onFileSelect($event)\"\r\n                                       style=\"display: none\" accept=\"*/*\" />\r\n                            </label>\r\n                        </div>\r\n\r\n                        <div *ngIf=\"selectedFile\" class=\"flex align-items-center justify-content-between p-3 bg-primary-50 border-round\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <i class=\"material-symbols-rounded text-primary\">attach_file</i>\r\n                                <div>\r\n                                    <p class=\"m-0 font-medium\">{{ selectedFile.name }}</p>\r\n                                    <small class=\"text-600\">{{ formatFileSize(selectedFile.size) }}</small>\r\n                                </div>\r\n                            </div>\r\n                            <button type=\"button\" class=\"p-button p-button-text p-button-rounded p-button-danger\"\r\n                                    (click)=\"removeSelectedFile()\">\r\n                                <i class=\"pi pi-times\"></i>\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                    <small class=\"p-error\" *ngIf=\"!selectedFile && attachmentFormSubmitted\">\r\n                        Please select a file\r\n                    </small>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12\">\r\n                <div class=\"field\">\r\n                    <label for=\"mimeType\" class=\"block text-900 font-medium mb-2\">MIME Type</label>\r\n                    <input pInputText id=\"mimeType\" formControlName=\"mime_type\"\r\n                           class=\"w-full\" placeholder=\"Auto-detected from file\" readonly />\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"flex justify-content-end gap-2 mt-4\">\r\n            <button pButton type=\"button\" label=\"Cancel\" class=\"p-button-outlined p-button-rounded\"\r\n                    (click)=\"hideAttachmentDialog()\"></button>\r\n            <button pButton type=\"submit\" label=\"Attach File\" class=\"p-button-rounded\"\r\n                    [disabled]=\"!selectedFile || attachmentForm.invalid || uploading\">\r\n                <i class=\"pi pi-spin pi-spinner mr-2\" *ngIf=\"uploading\"></i>\r\n                {{ uploading ? 'Uploading...' : 'Attach File' }}\r\n            </button>\r\n        </div>\r\n    </form>\r\n</p-dialog>"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;ICwBbC,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA4D;;;;;IAOxDD,EAAA,CAAAC,SAAA,YAEI;;;;IADAD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,SAAA,yDAA6E;;;;;IAEjFJ,EAAA,CAAAC,SAAA,YAA+D;;;;;;IAP3ED,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,aAAqF;IAAhCN,EAAA,CAAAO,UAAA,mBAAAC,yFAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAAN,MAAA,CAAAO,KAAA,CAAqB;IAAA,EAAC;IAChFhB,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,GACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAC,wEAAA,gBACkF,IAAAC,wEAAA,gBAEvB;IAEnEpB,EADI,CAAAqB,YAAA,EAAM,EACL;;;;;;IARDrB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,oBAAAO,MAAA,CAAAO,KAAA,CAA6B;IAEzBhB,EAAA,CAAAsB,SAAA,GACA;IADAtB,EAAA,CAAAuB,kBAAA,MAAAd,MAAA,CAAAe,MAAA,MACA;IAAIxB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;IAG7BhB,EAAA,CAAAsB,SAAA,EAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,KAAAhB,MAAA,CAAAO,KAAA,CAA6B;;;;;;IAhB7ChB,EADJ,CAAAM,cAAA,SAAI,aAC4E;IAA1DN,EAAA,CAAAO,UAAA,mBAAAmB,0EAAA;MAAA1B,EAAA,CAAAU,aAAA,CAAAiB,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAY,UAAA,CAAW,MAAM,CAAC;IAAA,EAAC;IAC1Cf,EAAA,CAAAM,cAAA,cAAoD;IAChDN,EAAA,CAAAiB,MAAA,kBACA;IAGAjB,EAHA,CAAAkB,UAAA,IAAAU,yDAAA,gBACkF,IAAAC,yDAAA,gBAE1B;IAEhE7B,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAAY,oEAAA,2BAAkD;IAWlD9B,EAAA,CAAAM,cAAA,aAAkC;IAAAN,EAAA,CAAAiB,MAAA,aAAM;IAC5CjB,EAD4C,CAAAqB,YAAA,EAAK,EAC5C;;;;IAlBWrB,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,YAA0B;IAG1BzB,EAAA,CAAAsB,SAAA,EAA0B;IAA1BtB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,SAAA,YAA0B;IAGRzB,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;;;;;IAoBxC/B,EAAA,CAAAM,cAAA,YACkC;IAC9BN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAI;;;;IAH+BrB,EAAA,CAAAE,UAAA,SAAA8B,aAAA,CAAAC,YAAA,EAAAjC,EAAA,CAAAkC,aAAA,CAAgC;IAE/DlC,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAS,aAAA,CAAAG,IAAA,sBACJ;;;;;IACAnC,EAAA,CAAAM,cAAA,WAAuC;IACnCN,EAAA,CAAAiB,MAAA,GACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAO;;;;IADHrB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAS,aAAA,CAAAG,IAAA,sBACJ;;;;;IAMInC,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAiB,MAAA,GACJ;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAS,aAAA,CAAAI,SAAA,aACJ;;;;;IAEApC,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAqC,WAAA,OAAAL,aAAA,CAAAM,SAAA,gBACJ;;;;;IAEAtC,EAAA,CAAAK,uBAAA,GAA0C;IACtCL,EAAA,CAAAiB,MAAA,GACJ;;;;;;IADIjB,EAAA,CAAAsB,SAAA,EACJ;IADItB,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAqC,WAAA,OAAAL,aAAA,CAAAO,SAAA,gBACJ;;;;;IAbZvC,EAAA,CAAAK,uBAAA,GAAkD;IAC9CL,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAK,uBAAA,OAAqC;IASjCL,EARA,CAAAkB,UAAA,IAAAsB,mFAAA,2BAA0C,IAAAC,mFAAA,2BAIA,IAAAC,mFAAA,2BAIA;;IAKlD1C,EAAA,CAAAqB,YAAA,EAAK;;;;;IAdarB,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,aAAAyC,MAAA,CAAA3B,KAAA,CAAsB;IACjBhB,EAAA,CAAAsB,SAAA,EAAyB;IAAzBtB,EAAA,CAAAE,UAAA,6BAAyB;IAIzBF,EAAA,CAAAsB,SAAA,EAAyB;IAAzBtB,EAAA,CAAAE,UAAA,6BAAyB;IAIzBF,EAAA,CAAAsB,SAAA,EAAyB;IAAzBtB,EAAA,CAAAE,UAAA,6BAAyB;;;;;;IAQhDF,EAAA,CAAAM,cAAA,iBAE6C;IAAzCN,EAAA,CAAAO,UAAA,mBAAAqC,uFAAA;MAAA5C,EAAA,CAAAU,aAAA,CAAAmC,GAAA;MAAA,MAAAb,aAAA,GAAAhC,EAAA,CAAAa,aAAA,GAAAD,SAAA;MAAA,MAAAT,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAA2C,kBAAA,CAAAd,aAAA,CAA8B;IAAA,EAAC;IACxChC,EAAA,CAAAM,cAAA,YAAiD;IAAAN,EAAA,CAAAiB,MAAA,eAAQ;IAC7DjB,EAD6D,CAAAqB,YAAA,EAAI,EACxD;;;;;IAjCLrB,EAHZ,CAAAM,cAAA,aAA2B,aACwB,cACA,YACU;IAAAN,EAAA,CAAAiB,MAAA,kBAAW;IAAAjB,EAAA,CAAAqB,YAAA,EAAI;IAKhErB,EAJA,CAAAkB,UAAA,IAAA6B,yDAAA,gBACkC,IAAAC,4DAAA,mBAGK;IAI/ChD,EADI,CAAAqB,YAAA,EAAM,EACL;IACLrB,EAAA,CAAAkB,UAAA,IAAA+B,oEAAA,2BAAkD;IAkBlDjD,EAAA,CAAAM,cAAA,SAAI;IACAN,EAAA,CAAAkB,UAAA,IAAAgC,8DAAA,qBAE6C;IAIrDlD,EADI,CAAAqB,YAAA,EAAK,EACJ;;;;;IAlCWrB,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAA8B,aAAA,CAAAC,YAAA,CAA6B;IAI1BjC,EAAA,CAAAsB,SAAA,EAA8B;IAA9BtB,EAAA,CAAAE,UAAA,UAAA8B,aAAA,CAAAC,YAAA,CAA8B;IAKfjC,EAAA,CAAAsB,SAAA,EAAkB;IAAlBtB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4B,eAAA,CAAkB;IAmBrB/B,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAE,UAAA,SAAA8B,aAAA,CAAAC,YAAA,CAA6B;;;;;IAWxDjC,EADJ,CAAAM,cAAA,SAAI,aAC6C;IAAAN,EAAA,CAAAiB,MAAA,4BAAqB;IACtEjB,EADsE,CAAAqB,YAAA,EAAK,EACtE;;;;;IAIDrB,EADJ,CAAAM,cAAA,SAAI,aAC6C;IAAAN,EAAA,CAAAiB,MAAA,6CAAsC;IACvFjB,EADuF,CAAAqB,YAAA,EAAK,EACvF;;;;;IAUbrB,EAAA,CAAAM,cAAA,SAAI;IAAAN,EAAA,CAAAiB,MAAA,qBAAc;IAAAjB,EAAA,CAAAqB,YAAA,EAAK;;;;;IAUXrB,EAAA,CAAAM,cAAA,gBAA0G;IACtGN,EAAA,CAAAiB,MAAA,8BACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAQ;;;;;;IASArB,EADJ,CAAAM,cAAA,cAA6E,YACf;IAAAN,EAAA,CAAAiB,MAAA,mBAAY;IAAAjB,EAAA,CAAAqB,YAAA,EAAI;IAC1ErB,EAAA,CAAAM,cAAA,YAAwB;IAAAN,EAAA,CAAAiB,MAAA,8CAAuC;IAAAjB,EAAA,CAAAqB,YAAA,EAAI;IACnErB,EAAA,CAAAM,cAAA,gBAA0F;IACtFN,EAAA,CAAAC,SAAA,YAA+B;IAC/BD,EAAA,CAAAiB,MAAA,oBACA;IAAAjB,EAAA,CAAAM,cAAA,gBAC4C;IADVN,EAAA,CAAAO,UAAA,oBAAA4C,sEAAAC,MAAA;MAAApD,EAAA,CAAAU,aAAA,CAAA2C,GAAA;MAAA,MAAAlD,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAUX,MAAA,CAAAmD,YAAA,CAAAF,MAAA,CAAoB;IAAA,EAAC;IAGzEpD,EAHQ,CAAAqB,YAAA,EAC4C,EACxC,EACN;;;;;;IAIErB,EAFR,CAAAM,cAAA,cAAiH,cAClE,YACU;IAAAN,EAAA,CAAAiB,MAAA,kBAAW;IAAAjB,EAAA,CAAAqB,YAAA,EAAI;IAE5DrB,EADJ,CAAAM,cAAA,UAAK,YAC0B;IAAAN,EAAA,CAAAiB,MAAA,GAAuB;IAAAjB,EAAA,CAAAqB,YAAA,EAAI;IACtDrB,EAAA,CAAAM,cAAA,gBAAwB;IAAAN,EAAA,CAAAiB,MAAA,GAAuC;IAEvEjB,EAFuE,CAAAqB,YAAA,EAAQ,EACrE,EACJ;IACNrB,EAAA,CAAAM,cAAA,iBACuC;IAA/BN,EAAA,CAAAO,UAAA,mBAAAgD,sEAAA;MAAAvD,EAAA,CAAAU,aAAA,CAAA8C,IAAA;MAAA,MAAArD,MAAA,GAAAH,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASX,MAAA,CAAAsD,kBAAA,EAAoB;IAAA,EAAC;IAClCzD,EAAA,CAAAC,SAAA,aAA2B;IAEnCD,EADI,CAAAqB,YAAA,EAAS,EACP;;;;IARiCrB,EAAA,CAAAsB,SAAA,GAAuB;IAAvBtB,EAAA,CAAA0D,iBAAA,CAAAvD,MAAA,CAAAwD,YAAA,CAAAxB,IAAA,CAAuB;IAC1BnC,EAAA,CAAAsB,SAAA,GAAuC;IAAvCtB,EAAA,CAAA0D,iBAAA,CAAAvD,MAAA,CAAAyD,cAAA,CAAAzD,MAAA,CAAAwD,YAAA,CAAAE,IAAA,EAAuC;;;;;IAS/E7D,EAAA,CAAAM,cAAA,gBAAwE;IACpEN,EAAA,CAAAiB,MAAA,6BACJ;IAAAjB,EAAA,CAAAqB,YAAA,EAAQ;;;;;IAkBZrB,EAAA,CAAAC,SAAA,YAA4D;;;AD5J5E,OAAM,MAAO6D,6BAA6B;EAcxCC,YACUC,iBAAoC,EACpCC,WAAwB,EACxBC,cAA8B;IAF9B,KAAAF,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IAhBhB,KAAAC,YAAY,GAAG,IAAIrE,OAAO,EAAQ;IACnC,KAAAsE,iBAAiB,GAAU,EAAE;IAC7B,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,OAAO,GAAY,KAAK;IAE/B;IACO,KAAAC,uBAAuB,GAAY,KAAK;IAExC,KAAAb,YAAY,GAAgB,IAAI;IAChC,KAAAc,SAAS,GAAY,KAAK;IAC1B,KAAAC,uBAAuB,GAAY,KAAK;IAavC,KAAAC,gBAAgB,GAAa,EAAE;IAEhC,KAAAC,IAAI,GAAa,CACtB;MAAE5D,KAAK,EAAE,WAAW;MAAEQ,MAAM,EAAE;IAAM,CAAE,EACtC;MAAER,KAAK,EAAE,WAAW;MAAEQ,MAAM,EAAE;IAAY,CAAE,EAC5C;MAAER,KAAK,EAAE,WAAW;MAAEQ,MAAM,EAAE;IAAY,CAAE,CAC7C;IAED,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAArB,SAAS,GAAW,CAAC;IAfnB,IAAI,CAACyE,cAAc,GAAG,IAAI,CAACZ,WAAW,CAACa,KAAK,CAAC;MAC3C3C,IAAI,EAAE,CAAC,EAAE,EAAEtC,UAAU,CAACkF,QAAQ,CAAC;MAC/B3C,SAAS,EAAE,CAAC,EAAE;KACf,CAAC;EACJ;EAaArB,UAAUA,CAACC,KAAa;IACtB,IAAI,IAAI,CAACS,SAAS,KAAKT,KAAK,EAAE;MAC5B,IAAI,CAACZ,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC,MAAM;MACL,IAAI,CAACqB,SAAS,GAAGT,KAAK;MACtB,IAAI,CAACZ,SAAS,GAAG,CAAC;IACpB;IAEA,IAAI,CAACgE,iBAAiB,CAACY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACnC,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,CAAC,EAAEjE,KAAK,CAAC;MAC9C,MAAMqE,MAAM,GAAG,IAAI,CAACD,gBAAgB,CAACF,CAAC,EAAElE,KAAK,CAAC;MAE9C,IAAIsE,MAAM,GAAG,CAAC;MAEd,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,KAC7C,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAEC,MAAM,GAAG,CAAC,CAAC,KACjD,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC/DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,CAAC,CAAC,KACnCC,MAAM,GAAIH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAIF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAG;MAEhE,OAAO,IAAI,CAACjF,SAAS,GAAGkF,MAAM;IAChC,CAAC,CAAC;EACJ;EAEA;EACAF,gBAAgBA,CAACI,IAAS,EAAExE,KAAa;IACvC,IAAI,CAACwE,IAAI,IAAI,CAACxE,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIA,KAAK,CAACyE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,OAAOD,IAAI,CAACxE,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,OAAOA,KAAK,CAAC0E,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAC,EAAEL,IAAI,CAAC;IAChE;EACF;EAEAM,QAAQA,CAAA;IACN,IAAI,CAAC9B,iBAAiB,CAAC+B,QAAQ,CAC5BC,IAAI,CAACjG,SAAS,CAAC,IAAI,CAACoE,YAAY,CAAC,CAAC,CAClC8B,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAC7B,KAAK,GAAG6B,QAAQ,EAAE7B,KAAK;QAC5B,IAAI,CAACC,WAAW,GAAG4B,QAAQ,EAAE5B,WAAW;QAExC,IAAI,IAAI,CAACA,WAAW,EAAE;UACpB,IAAI,CAAC6B,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC;IAEJ,IAAI,CAACxB,gBAAgB,GAAG,IAAI,CAACC,IAAI;EACnC;EAEQuB,eAAeA,CAAA;IACrB,IAAI,CAAC5B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACP,iBAAiB,CAACoC,iBAAiB,CAAC,IAAI,CAAC9B,WAAW,CAAC,CACvD0B,IAAI,CAACjG,SAAS,CAAC,IAAI,CAACoE,YAAY,CAAC,CAAC,CAClC8B,SAAS,CAAC;MACTI,IAAI,EAAGH,QAAa,IAAI;QACtB,IAAI,CAAC9B,iBAAiB,GAAG8B,QAAQ,EAAEV,IAAI,IAAI,EAAE;QAC7C,IAAI,CAACjB,OAAO,GAAG,KAAK;MACtB,CAAC;MACD+B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAAClC,iBAAiB,GAAG,EAAE;QAC3B,IAAI,CAACG,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEAzB,kBAAkBA,CAAC0D,UAAe;IAChC,IAAIA,UAAU,CAACvE,YAAY,EAAE;MAC3BwE,MAAM,CAACC,IAAI,CAACF,UAAU,CAACvE,YAAY,EAAE,QAAQ,CAAC;IAChD;EACF;EAEA,IAAIF,eAAeA,CAAA;IACjB,OAAO,IAAI,CAAC4C,gBAAgB;EAC9B;EAEA,IAAI5C,eAAeA,CAAC4E,GAAU;IAC5B,IAAI,CAAChC,gBAAgB,GAAG,IAAI,CAACC,IAAI,CAACgC,MAAM,CAACC,GAAG,IAAIF,GAAG,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;EACpE;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,UAAU,GAAG,IAAI,CAACtC,gBAAgB,CAACqC,KAAK,CAACE,SAAS,CAAC;IACzD,IAAI,CAACvC,gBAAgB,CAACwC,MAAM,CAACH,KAAK,CAACE,SAAS,EAAE,CAAC,CAAC;IAChD,IAAI,CAACvC,gBAAgB,CAACwC,MAAM,CAACH,KAAK,CAACI,SAAS,EAAE,CAAC,EAAEH,UAAU,CAAC;EAC9D;EAGAI,WAAWA,CAAA;IACT,IAAI,CAAClD,YAAY,CAACkC,IAAI,EAAE;IACxB,IAAI,CAAClC,YAAY,CAACmD,QAAQ,EAAE;EAC9B;;;uBAjIWxD,6BAA6B,EAAA9D,EAAA,CAAAuH,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAzH,EAAA,CAAAuH,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA3H,EAAA,CAAAuH,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA7B/D,6BAA6B;MAAAgE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCdlCpI,EAFR,CAAAM,cAAA,aAA2D,aACyC,YAC7C;UAAAN,EAAA,CAAAiB,MAAA,kBAAW;UAAAjB,EAAA,CAAAqB,YAAA,EAAK;UAG3DrB,EADJ,CAAAM,cAAA,aAA2C,kBAEwD;UAAtCN,EAAA,CAAAO,UAAA,mBAAA+H,iEAAA;YAAAtI,EAAA,CAAAU,aAAA,CAAA6H,GAAA;YAAA,OAAAvI,EAAA,CAAAc,WAAA,CAASuH,GAAA,CAAAG,uBAAA,EAAyB;UAAA,EAAC;UAD5FxI,EAAA,CAAAqB,YAAA,EAC+F;UAE/FrB,EAAA,CAAAM,cAAA,uBAE+I;UAF/GN,EAAA,CAAAyI,gBAAA,2BAAAC,8EAAAtF,MAAA;YAAApD,EAAA,CAAAU,aAAA,CAAA6H,GAAA;YAAAvI,EAAA,CAAA2I,kBAAA,CAAAN,GAAA,CAAAtG,eAAA,EAAAqB,MAAA,MAAAiF,GAAA,CAAAtG,eAAA,GAAAqB,MAAA;YAAA,OAAApD,EAAA,CAAAc,WAAA,CAAAsC,MAAA;UAAA,EAA6B;UAKrEpD,EAFQ,CAAAqB,YAAA,EAAgB,EACd,EACJ;UAIFrB,EAFJ,CAAAM,cAAA,aAAuB,oBAI0B;UAAzCN,EAAA,CAAAO,UAAA,0BAAAqI,uEAAAxF,MAAA;YAAApD,EAAA,CAAAU,aAAA,CAAA6H,GAAA;YAAA,OAAAvI,EAAA,CAAAc,WAAA,CAAgBuH,GAAA,CAAAtB,eAAA,CAAA3D,MAAA,CAAuB;UAAA,EAAC;UA2ExCpD,EAzEA,CAAAkB,UAAA,KAAA2H,qDAAA,yBAAgC,KAAAC,qDAAA,2BA0BmC,KAAAC,qDAAA,0BA0C7B,KAAAC,qDAAA,0BAKD;UAOjDhJ,EAFQ,CAAAqB,YAAA,EAAU,EACR,EACJ;UAGNrB,EAAA,CAAAM,cAAA,oBACiD;UADxBN,EAAA,CAAAyI,gBAAA,2BAAAQ,0EAAA7F,MAAA;YAAApD,EAAA,CAAAU,aAAA,CAAA6H,GAAA;YAAAvI,EAAA,CAAA2I,kBAAA,CAAAN,GAAA,CAAA7D,uBAAA,EAAApB,MAAA,MAAAiF,GAAA,CAAA7D,uBAAA,GAAApB,MAAA;YAAA,OAAApD,EAAA,CAAAc,WAAA,CAAAsC,MAAA;UAAA,EAAqC;UAE1DpD,EAAA,CAAAkB,UAAA,KAAAgI,qDAAA,yBAAgC;UAIhClJ,EAAA,CAAAM,cAAA,gBAAqE;UAAlCN,EAAA,CAAAO,UAAA,sBAAA4I,iEAAA;YAAAnJ,EAAA,CAAAU,aAAA,CAAA6H,GAAA;YAAA,OAAAvI,EAAA,CAAAc,WAAA,CAAYuH,GAAA,CAAAe,kBAAA,EAAoB;UAAA,EAAC;UAIpDpJ,EAHZ,CAAAM,cAAA,eAAkB,eACM,eACG,iBACqD;UAAAN,EAAA,CAAAiB,MAAA,mBAAW;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UACvFrB,EAAA,CAAAC,SAAA,iBACsD;UACtDD,EAAA,CAAAkB,UAAA,KAAAmI,+CAAA,oBAA0G;UAIlHrJ,EADI,CAAAqB,YAAA,EAAM,EACJ;UAIErB,EAFR,CAAAM,cAAA,eAAoB,eACG,iBACgC;UAAAN,EAAA,CAAAiB,MAAA,qBAAa;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UACpErB,EAAA,CAAAM,cAAA,eAAkG;UAY9FN,EAXA,CAAAkB,UAAA,KAAAoI,6CAAA,kBAA6E,KAAAC,6CAAA,mBAWoC;UAarHvJ,EAAA,CAAAqB,YAAA,EAAM;UACNrB,EAAA,CAAAkB,UAAA,KAAAsI,+CAAA,oBAAwE;UAIhFxJ,EADI,CAAAqB,YAAA,EAAM,EACJ;UAIErB,EAFR,CAAAM,cAAA,eAAoB,eACG,iBAC+C;UAAAN,EAAA,CAAAiB,MAAA,iBAAS;UAAAjB,EAAA,CAAAqB,YAAA,EAAQ;UAC/ErB,EAAA,CAAAC,SAAA,iBACuE;UAGnFD,EAFQ,CAAAqB,YAAA,EAAM,EACJ,EACJ;UAGFrB,EADJ,CAAAM,cAAA,eAAiD,kBAEJ;UAAjCN,EAAA,CAAAO,UAAA,mBAAAkJ,gEAAA;YAAAzJ,EAAA,CAAAU,aAAA,CAAA6H,GAAA;YAAA,OAAAvI,EAAA,CAAAc,WAAA,CAASuH,GAAA,CAAAqB,oBAAA,EAAsB;UAAA,EAAC;UAAC1J,EAAA,CAAAqB,YAAA,EAAS;UAClDrB,EAAA,CAAAM,cAAA,kBAC0E;UACtEN,EAAA,CAAAkB,UAAA,KAAAyI,2CAAA,gBAAwD;UACxD3J,EAAA,CAAAiB,MAAA,IACJ;UAGZjB,EAHY,CAAAqB,YAAA,EAAS,EACP,EACH,EACA;;;;UA3KKrB,EAAA,CAAAsB,SAAA,GAAmC;UAACtB,EAApC,CAAAE,UAAA,oCAAmC,iBAAiB;UAEzCF,EAAA,CAAAsB,SAAA,EAAgB;UAAhBtB,EAAA,CAAAE,UAAA,YAAAmI,GAAA,CAAAzD,IAAA,CAAgB;UAAC5E,EAAA,CAAA4J,gBAAA,YAAAvB,GAAA,CAAAtG,eAAA,CAA6B;UAEzD/B,EAAA,CAAAE,UAAA,2IAA0I;UAOpIF,EAAA,CAAAsB,SAAA,GAA2B;UACkCtB,EAD7D,CAAAE,UAAA,UAAAmI,GAAA,CAAAjE,iBAAA,CAA2B,WAAwB,mBAAmB,YAAAiE,GAAA,CAAA9D,OAAA,CAAoB,oBACvD,4BAAqD;UAsF/CvE,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAA6J,UAAA,CAAA7J,EAAA,CAAA8J,eAAA,KAAAC,GAAA,EAA4B;UAAjF/J,EAAA,CAAAE,UAAA,eAAc;UAACF,EAAA,CAAA4J,gBAAA,YAAAvB,GAAA,CAAA7D,uBAAA,CAAqC;UAC1DxE,EADwF,CAAAE,UAAA,qBAAoB,oBACzF;UAKbF,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAE,UAAA,cAAAmI,GAAA,CAAAxD,cAAA,CAA4B;UAOM7E,EAAA,CAAAsB,SAAA,GAAgF;UAAhFtB,EAAA,CAAAE,UAAA,WAAA8J,QAAA,GAAA3B,GAAA,CAAAxD,cAAA,CAAAoF,GAAA,2BAAAD,QAAA,CAAAE,OAAA,OAAAF,QAAA,GAAA3B,GAAA,CAAAxD,cAAA,CAAAoF,GAAA,2BAAAD,QAAA,CAAAG,OAAA,EAAgF;UAU9FnK,EAAA,CAAAsB,SAAA,GAAmB;UAAnBtB,EAAA,CAAAE,UAAA,UAAAmI,GAAA,CAAA1E,YAAA,CAAmB;UAWnB3D,EAAA,CAAAsB,SAAA,EAAkB;UAAlBtB,EAAA,CAAAE,UAAA,SAAAmI,GAAA,CAAA1E,YAAA,CAAkB;UAcJ3D,EAAA,CAAAsB,SAAA,EAA8C;UAA9CtB,EAAA,CAAAE,UAAA,UAAAmI,GAAA,CAAA1E,YAAA,IAAA0E,GAAA,CAAA3D,uBAAA,CAA8C;UAmBtE1E,EAAA,CAAAsB,SAAA,GAAiE;UAAjEtB,EAAA,CAAAE,UAAA,cAAAmI,GAAA,CAAA1E,YAAA,IAAA0E,GAAA,CAAAxD,cAAA,CAAAqF,OAAA,IAAA7B,GAAA,CAAA5D,SAAA,CAAiE;UAC9BzE,EAAA,CAAAsB,SAAA,EAAe;UAAftB,EAAA,CAAAE,UAAA,SAAAmI,GAAA,CAAA5D,SAAA,CAAe;UACtDzE,EAAA,CAAAsB,SAAA,EACJ;UADItB,EAAA,CAAAuB,kBAAA,MAAA8G,GAAA,CAAA5D,SAAA,uCACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}