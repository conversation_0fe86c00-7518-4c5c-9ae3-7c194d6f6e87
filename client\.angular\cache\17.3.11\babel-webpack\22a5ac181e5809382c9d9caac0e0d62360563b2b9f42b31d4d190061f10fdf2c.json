{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { Observable, throwError, of } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport { CMS_APIContstant } from '../../constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class MediaUploadService {\n  constructor(http) {\n    this.http = http;\n    this.folderCache = new Map();\n    this.targetFolder = 'CRM/Attachments';\n  }\n  /**\n   * Upload file to media library with specified path\n   * @param file - File to upload\n   * @param path - Upload path (e.g., 'CRM/Attachments')\n   * @returns Observable with upload response\n   */\n  uploadFile(file, path = 'CRM/Attachments') {\n    const formData = new FormData();\n    formData.append('files', file);\n    formData.append('path', path);\n    // Set headers for file upload\n    const headers = new HttpHeaders();\n    // Don't set Content-Type header, let browser set it with boundary for multipart/form-data\n    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, {\n      headers\n    });\n  }\n  /**\n   * Enhanced upload file with folder creation and CRM attachment record\n   * @param file - File to upload\n   * @param activityId - Activity ID for CRM attachment record\n   * @param createFolderStructure - Whether to create folder structure (default: true)\n   * @returns Observable with upload result and CRM attachment record\n   */\n  uploadFileWithCRMAttachment(file, activityId, createFolderStructure = true) {\n    // Validate file first\n    const validation = this.validateFile(file);\n    if (!validation.isValid) {\n      return throwError(() => new Error(validation.error));\n    }\n    console.log(`Starting upload: ${file.name} (${this.formatFileSize(file.size)})`);\n    // Step 1: Ensure folder exists if required\n    const folderObservable = createFolderStructure ? this.ensureFolderExists(this.targetFolder) : of(null);\n    return folderObservable.pipe(switchMap(folderId => {\n      console.log(`Target folder ID: ${folderId || 'root'}`);\n      // Step 2: Upload file to Strapi\n      return this.uploadToStrapi(file, folderId);\n    }), switchMap(uploadResult => {\n      console.log(`✅ Upload successful!`);\n      console.log(`   File: ${file.name}`);\n      console.log(`   Strapi ID: ${uploadResult.id}`);\n      console.log(`   URL: ${uploadResult.url}`);\n      console.log(`   Folder: ${this.targetFolder}`);\n      // Step 3: Create CRM attachment record if activityId provided\n      if (activityId) {\n        return this.createCRMAttachmentRecord(uploadResult, activityId).pipe(map(crmRecord => ({\n          success: true,\n          file: uploadResult,\n          crmAttachment: crmRecord,\n          message: 'File uploaded and CRM attachment record created successfully'\n        })));\n      } else {\n        return of({\n          success: true,\n          file: uploadResult,\n          message: 'File uploaded successfully'\n        });\n      }\n    }), catchError(error => {\n      console.error(`❌ Upload failed: ${error.message}`);\n      return throwError(() => ({\n        success: false,\n        error: error.message,\n        message: 'File upload failed'\n      }));\n    }));\n  }\n  /**\n   * Upload multiple files to media library\n   * @param files - Array of files to upload\n   * @param path - Upload path (e.g., 'CRM/Attachments')\n   * @returns Observable with upload response\n   */\n  uploadMultipleFiles(files, path = 'CRM/Attachments') {\n    const formData = new FormData();\n    files.forEach(file => {\n      formData.append('files', file);\n    });\n    formData.append('path', path);\n    const headers = new HttpHeaders();\n    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, {\n      headers\n    });\n  }\n  /**\n   * Upload file with progress tracking\n   * @param file - File to upload\n   * @param path - Upload path (e.g., 'CRM/Attachments')\n   * @returns Observable with upload progress and response\n   */\n  uploadFileWithProgress(file, path = 'CRM/Attachments') {\n    const formData = new FormData();\n    formData.append('files', file);\n    formData.append('path', path);\n    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, {\n      reportProgress: true,\n      observe: 'events'\n    });\n  }\n  /**\n   * Delete uploaded file\n   * @param fileId - ID of the file to delete\n   * @returns Observable with delete response\n   */\n  deleteFile(fileId) {\n    return this.http.delete(`${CMS_APIContstant.MEDIA_UPLOAD}/${fileId}`);\n  }\n  /**\n   * Get file details by ID\n   * @param fileId - ID of the file\n   * @returns Observable with file details\n   */\n  getFileDetails(fileId) {\n    return this.http.get(`${CMS_APIContstant.MEDIA_UPLOAD}/${fileId}`);\n  }\n  /**\n   * Get files by path\n   * @param path - Path to search files (e.g., 'CRM/Attachments')\n   * @returns Observable with files list\n   */\n  getFilesByPath(path) {\n    return this.http.get(`${CMS_APIContstant.MEDIA_UPLOAD}?path=${encodeURIComponent(path)}`);\n  }\n  /**\n   * Validate file before upload\n   * @param file - File to validate\n   * @param maxSize - Maximum file size in bytes (default: 10MB)\n   * @param allowedTypes - Array of allowed MIME types\n   * @returns Validation result\n   */\n  validateFile(file, maxSize = 10 * 1024 * 1024,\n  // 10MB default\n  allowedTypes) {\n    // Check file size\n    if (file.size > maxSize) {\n      return {\n        isValid: false,\n        error: `File size exceeds maximum limit of ${this.formatFileSize(maxSize)}`\n      };\n    }\n    // Check file type if specified\n    if (allowedTypes && allowedTypes.length > 0) {\n      if (!allowedTypes.includes(file.type)) {\n        return {\n          isValid: false,\n          error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`\n        };\n      }\n    }\n    return {\n      isValid: true\n    };\n  }\n  /**\n   * Format file size for display\n   * @param bytes - File size in bytes\n   * @returns Formatted file size string\n   */\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n  /**\n   * Generate unique filename to avoid conflicts\n   * @param originalName - Original filename\n   * @returns Unique filename with timestamp\n   */\n  generateUniqueFilename(originalName) {\n    const timestamp = Date.now();\n    const extension = originalName.substring(originalName.lastIndexOf('.'));\n    const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.'));\n    return `${nameWithoutExt}_${timestamp}${extension}`;\n  }\n  /**\n   * Upload file to Strapi with folder support\n   * @param file - File to upload\n   * @param folderId - Optional folder ID\n   * @returns Observable with upload result\n   */\n  uploadToStrapi(file, folderId = null) {\n    const formData = new FormData();\n    formData.append('file', file, file.name);\n    // Add folder ID if available\n    if (folderId) {\n      formData.append('folderId', folderId.toString());\n    }\n    const headers = new HttpHeaders();\n    // Don't set Content-Type header for multipart/form-data\n    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, {\n      headers\n    }).pipe(map(response => {\n      // Handle plugin response format\n      let uploadedFile;\n      if (response && response.data && Array.isArray(response.data)) {\n        uploadedFile = response.data[0];\n      } else if (response && Array.isArray(response)) {\n        uploadedFile = response[0];\n      } else if (response) {\n        uploadedFile = response;\n      } else {\n        throw new Error('Invalid response from Strapi');\n      }\n      if (!uploadedFile) {\n        throw new Error('No file data in response');\n      }\n      return uploadedFile;\n    }), catchError(error => {\n      console.error('Upload to Strapi failed:', error);\n      return throwError(() => error);\n    }));\n  }\n  /**\n   * Ensure folder structure exists\n   * @param folderPath - Path to create (e.g., 'CRM/Attachments')\n   * @returns Observable with folder ID\n   */\n  ensureFolderExists(folderPath) {\n    // Check cache first\n    if (this.folderCache.has(folderPath)) {\n      return of(this.folderCache.get(folderPath) || null);\n    }\n    // Split path into parts\n    const pathParts = folderPath.split('/').filter(part => part && part !== '.');\n    let currentPath = '';\n    // Create folders sequentially\n    return this.createFoldersSequentially(pathParts, null, currentPath, folderPath);\n  }\n  /**\n   * Create folders sequentially in hierarchy\n   */\n  createFoldersSequentially(pathParts, currentFolderId, currentPath, fullPath) {\n    if (pathParts.length === 0) {\n      // Cache the final folder ID\n      if (currentFolderId) {\n        this.folderCache.set(fullPath, currentFolderId);\n      }\n      return of(currentFolderId);\n    }\n    const folderName = pathParts[0];\n    const remainingParts = pathParts.slice(1);\n    currentPath = currentPath ? `${currentPath}/${folderName}` : folderName;\n    // Check if this partial path is cached\n    if (this.folderCache.has(currentPath)) {\n      const cachedId = this.folderCache.get(currentPath) || null;\n      return this.createFoldersSequentially(remainingParts, cachedId, currentPath, fullPath);\n    }\n    // Create or get folder\n    return this.createOrGetFolder(folderName, currentFolderId).pipe(switchMap(folderId => {\n      if (folderId === null) {\n        console.warn(`Failed to create folder: ${folderName}`);\n        return of(null);\n      }\n      // Cache the folder ID\n      this.folderCache.set(currentPath, folderId);\n      // Continue with remaining parts\n      return this.createFoldersSequentially(remainingParts, folderId, currentPath, fullPath);\n    }));\n  }\n  /**\n   * Create or get existing folder\n   * @param folderName - Name of the folder\n   * @param parentId - Parent folder ID\n   * @returns Observable with folder ID\n   */\n  createOrGetFolder(folderName, parentId = null) {\n    // First, try to find if folder already exists\n    return this.findFolder(folderName, parentId).pipe(switchMap(existingFolder => {\n      if (existingFolder) {\n        console.log(`Found existing folder: ${folderName} (ID: ${existingFolder.id})`);\n        return of(existingFolder.id);\n      }\n      // Create new folder\n      const requestBody = {\n        name: folderName\n      };\n      if (parentId) {\n        requestBody.parentId = parentId;\n      }\n      const headers = new HttpHeaders({\n        'Content-Type': 'application/json'\n      });\n      return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD.replace('/files', '/folders')}`, requestBody, {\n        headers\n      }).pipe(map(response => {\n        if (!response || !response.id) {\n          throw new Error('Invalid response when creating folder');\n        }\n        console.log(`Created folder: ${folderName} (ID: ${response.id})`);\n        return response.id;\n      }), catchError(error => {\n        // Handle folder already exists error\n        if (error.status === 400 || error.status === 500) {\n          return this.findFolder(folderName, parentId).pipe(map(folder => {\n            if (folder) {\n              console.log(`Found folder after conflict: ${folderName} (ID: ${folder.id})`);\n              return folder.id;\n            }\n            return null;\n          }));\n        }\n        // If media library plugin not available, return null\n        if (error.status === 404 || error.status === 403) {\n          console.warn(`Media library plugin not available or permission denied`);\n          return new Observable(observer => {\n            observer.next(null);\n            observer.complete();\n          });\n        }\n        console.error(`Error creating folder ${folderName}: ${error.message}`);\n        return new Observable(observer => {\n          observer.next(null);\n          observer.complete();\n        });\n      }));\n    }));\n  }\n  /**\n   * Find existing folder by name and parent\n   * @param folderName - Name of the folder to find\n   * @param parentId - Parent folder ID\n   * @returns Observable with folder data or null\n   */\n  findFolder(folderName, parentId = null) {\n    const params = new URLSearchParams();\n    if (parentId) {\n      params.append('parentId', parentId.toString());\n    }\n    const url = `${CMS_APIContstant.MEDIA_UPLOAD.replace('/files', '/folders')}?${params.toString()}`;\n    return this.http.get(url).pipe(map(response => {\n      if (response && Array.isArray(response)) {\n        return response.find(folder => folder.name === folderName);\n      }\n      return null;\n    }), catchError(error => {\n      if (error.status === 404 || error.status === 403) {\n        return new Observable(observer => {\n          observer.next(null);\n          observer.complete();\n        });\n      }\n      console.warn(`Error searching for folder ${folderName}: ${error.message}`);\n      return new Observable(observer => {\n        observer.next(null);\n        observer.complete();\n      });\n    }));\n  }\n  /**\n   * Create CRM attachment record\n   * @param uploadResult - Upload result from Strapi\n   * @param activityId - Activity ID for the attachment\n   * @returns Observable with CRM attachment record\n   */\n  createCRMAttachmentRecord(uploadResult, activityId) {\n    const attachmentData = {\n      mime_type: uploadResult.mime || uploadResult.mimeType || 'application/octet-stream',\n      name: uploadResult.name || uploadResult.filename,\n      link_web_uri: uploadResult.url,\n      activity_id: activityId\n    };\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n    return this.http.post(`${CMS_APIContstant.CRM_ATTACHMENTS}`, attachmentData, {\n      headers\n    }).pipe(map(response => {\n      console.log(`✅ CRM attachment record created: ${response.id}`);\n      return response;\n    }), catchError(error => {\n      console.error('Failed to create CRM attachment record:', error);\n      return throwError(() => error);\n    }));\n  }\n  static {\n    this.ɵfac = function MediaUploadService_Factory(t) {\n      return new (t || MediaUploadService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MediaUploadService,\n      factory: MediaUploadService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "Observable", "throwError", "of", "map", "catchError", "switchMap", "CMS_APIContstant", "MediaUploadService", "constructor", "http", "folderCache", "Map", "targetFolder", "uploadFile", "file", "path", "formData", "FormData", "append", "headers", "post", "MEDIA_UPLOAD", "uploadFileWithCRMAttachment", "activityId", "createFolderStructure", "validation", "validateFile", "<PERSON><PERSON><PERSON><PERSON>", "Error", "error", "console", "log", "name", "formatFileSize", "size", "folderObservable", "ensureFolderExists", "pipe", "folderId", "uploadToStrapi", "uploadResult", "id", "url", "createCRMAttachmentRecord", "crmRecord", "success", "crmAttachment", "message", "uploadMultipleFiles", "files", "for<PERSON>ach", "uploadFileWithProgress", "reportProgress", "observe", "deleteFile", "fileId", "delete", "getFileDetails", "get", "getFilesByPath", "encodeURIComponent", "maxSize", "allowedTypes", "length", "includes", "type", "join", "bytes", "k", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "generateUniqueFilename", "originalName", "timestamp", "Date", "now", "extension", "substring", "lastIndexOf", "nameWithoutExt", "toString", "response", "uploadedFile", "data", "Array", "isArray", "folderPath", "has", "pathParts", "split", "filter", "part", "currentPath", "createFoldersSequentially", "currentFolderId", "fullPath", "set", "folderName", "remainingParts", "slice", "cachedId", "createOrGetFolder", "warn", "parentId", "findFolder", "existingFolder", "requestBody", "replace", "status", "folder", "observer", "next", "complete", "params", "URLSearchParams", "find", "attachmentData", "mime_type", "mime", "mimeType", "filename", "link_web_uri", "activity_id", "CRM_ATTACHMENTS", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\services\\media-upload.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, throwError, of } from 'rxjs';\nimport { map, catchError, switchMap } from 'rxjs/operators';\nimport { CMS_APIContstant } from '../../constants/api.constants';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class MediaUploadService {\n  private folderCache = new Map<string, number>();\n  private readonly targetFolder = 'CRM/Attachments';\n\n  constructor(private http: HttpClient) { }\n\n  /**\n   * Upload file to media library with specified path\n   * @param file - File to upload\n   * @param path - Upload path (e.g., 'CRM/Attachments')\n   * @returns Observable with upload response\n   */\n  uploadFile(file: File, path: string = 'CRM/Attachments'): Observable<any> {\n    const formData = new FormData();\n    formData.append('files', file);\n    formData.append('path', path);\n\n    // Set headers for file upload\n    const headers = new HttpHeaders();\n    // Don't set Content-Type header, let browser set it with boundary for multipart/form-data\n\n    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, { headers });\n  }\n\n  /**\n   * Enhanced upload file with folder creation and CRM attachment record\n   * @param file - File to upload\n   * @param activityId - Activity ID for CRM attachment record\n   * @param createFolderStructure - Whether to create folder structure (default: true)\n   * @returns Observable with upload result and CRM attachment record\n   */\n  uploadFileWithCRMAttachment(\n    file: File,\n    activityId?: string,\n    createFolderStructure: boolean = true\n  ): Observable<any> {\n    // Validate file first\n    const validation = this.validateFile(file);\n    if (!validation.isValid) {\n      return throwError(() => new Error(validation.error));\n    }\n\n    console.log(`Starting upload: ${file.name} (${this.formatFileSize(file.size)})`);\n\n    // Step 1: Ensure folder exists if required\n    const folderObservable = createFolderStructure\n      ? this.ensureFolderExists(this.targetFolder)\n      : of(null);\n\n    return folderObservable.pipe(\n      switchMap((folderId: number | null) => {\n        console.log(`Target folder ID: ${folderId || 'root'}`);\n\n        // Step 2: Upload file to Strapi\n        return this.uploadToStrapi(file, folderId);\n      }),\n      switchMap((uploadResult: any) => {\n        console.log(`✅ Upload successful!`);\n        console.log(`   File: ${file.name}`);\n        console.log(`   Strapi ID: ${uploadResult.id}`);\n        console.log(`   URL: ${uploadResult.url}`);\n        console.log(`   Folder: ${this.targetFolder}`);\n\n        // Step 3: Create CRM attachment record if activityId provided\n        if (activityId) {\n          return this.createCRMAttachmentRecord(uploadResult, activityId).pipe(\n            map((crmRecord: any) => ({\n              success: true,\n              file: uploadResult,\n              crmAttachment: crmRecord,\n              message: 'File uploaded and CRM attachment record created successfully'\n            }))\n          );\n        } else {\n          return of({\n            success: true,\n            file: uploadResult,\n            message: 'File uploaded successfully'\n          });\n        }\n      }),\n      catchError((error: any) => {\n        console.error(`❌ Upload failed: ${error.message}`);\n        return throwError(() => ({\n          success: false,\n          error: error.message,\n          message: 'File upload failed'\n        }));\n      })\n    );\n  }\n\n  /**\n   * Upload multiple files to media library\n   * @param files - Array of files to upload\n   * @param path - Upload path (e.g., 'CRM/Attachments')\n   * @returns Observable with upload response\n   */\n  uploadMultipleFiles(files: File[], path: string = 'CRM/Attachments'): Observable<any> {\n    const formData = new FormData();\n    \n    files.forEach(file => {\n      formData.append('files', file);\n    });\n    formData.append('path', path);\n\n    const headers = new HttpHeaders();\n    \n    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, { headers });\n  }\n\n  /**\n   * Upload file with progress tracking\n   * @param file - File to upload\n   * @param path - Upload path (e.g., 'CRM/Attachments')\n   * @returns Observable with upload progress and response\n   */\n  uploadFileWithProgress(file: File, path: string = 'CRM/Attachments'): Observable<any> {\n    const formData = new FormData();\n    formData.append('files', file);\n    formData.append('path', path);\n\n    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, {\n      reportProgress: true,\n      observe: 'events'\n    });\n  }\n\n  /**\n   * Delete uploaded file\n   * @param fileId - ID of the file to delete\n   * @returns Observable with delete response\n   */\n  deleteFile(fileId: string): Observable<any> {\n    return this.http.delete(`${CMS_APIContstant.MEDIA_UPLOAD}/${fileId}`);\n  }\n\n  /**\n   * Get file details by ID\n   * @param fileId - ID of the file\n   * @returns Observable with file details\n   */\n  getFileDetails(fileId: string): Observable<any> {\n    return this.http.get(`${CMS_APIContstant.MEDIA_UPLOAD}/${fileId}`);\n  }\n\n  /**\n   * Get files by path\n   * @param path - Path to search files (e.g., 'CRM/Attachments')\n   * @returns Observable with files list\n   */\n  getFilesByPath(path: string): Observable<any> {\n    return this.http.get(`${CMS_APIContstant.MEDIA_UPLOAD}?path=${encodeURIComponent(path)}`);\n  }\n\n  /**\n   * Validate file before upload\n   * @param file - File to validate\n   * @param maxSize - Maximum file size in bytes (default: 10MB)\n   * @param allowedTypes - Array of allowed MIME types\n   * @returns Validation result\n   */\n  validateFile(\n    file: File, \n    maxSize: number = 10 * 1024 * 1024, // 10MB default\n    allowedTypes?: string[]\n  ): { isValid: boolean; error?: string } {\n    \n    // Check file size\n    if (file.size > maxSize) {\n      return {\n        isValid: false,\n        error: `File size exceeds maximum limit of ${this.formatFileSize(maxSize)}`\n      };\n    }\n\n    // Check file type if specified\n    if (allowedTypes && allowedTypes.length > 0) {\n      if (!allowedTypes.includes(file.type)) {\n        return {\n          isValid: false,\n          error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`\n        };\n      }\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Format file size for display\n   * @param bytes - File size in bytes\n   * @returns Formatted file size string\n   */\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  /**\n   * Generate unique filename to avoid conflicts\n   * @param originalName - Original filename\n   * @returns Unique filename with timestamp\n   */\n  generateUniqueFilename(originalName: string): string {\n    const timestamp = Date.now();\n    const extension = originalName.substring(originalName.lastIndexOf('.'));\n    const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.'));\n    return `${nameWithoutExt}_${timestamp}${extension}`;\n  }\n\n  /**\n   * Upload file to Strapi with folder support\n   * @param file - File to upload\n   * @param folderId - Optional folder ID\n   * @returns Observable with upload result\n   */\n  private uploadToStrapi(file: File, folderId: number | null = null): Observable<any> {\n    const formData = new FormData();\n    formData.append('file', file, file.name);\n\n    // Add folder ID if available\n    if (folderId) {\n      formData.append('folderId', folderId.toString());\n    }\n\n    const headers = new HttpHeaders();\n    // Don't set Content-Type header for multipart/form-data\n\n    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, { headers }).pipe(\n      map((response: any) => {\n        // Handle plugin response format\n        let uploadedFile;\n        if (response && response.data && Array.isArray(response.data)) {\n          uploadedFile = response.data[0];\n        } else if (response && Array.isArray(response)) {\n          uploadedFile = response[0];\n        } else if (response) {\n          uploadedFile = response;\n        } else {\n          throw new Error('Invalid response from Strapi');\n        }\n\n        if (!uploadedFile) {\n          throw new Error('No file data in response');\n        }\n\n        return uploadedFile;\n      }),\n      catchError((error: any) => {\n        console.error('Upload to Strapi failed:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  /**\n   * Ensure folder structure exists\n   * @param folderPath - Path to create (e.g., 'CRM/Attachments')\n   * @returns Observable with folder ID\n   */\n  private ensureFolderExists(folderPath: string): Observable<number | null> {\n    // Check cache first\n    if (this.folderCache.has(folderPath)) {\n      return of(this.folderCache.get(folderPath) || null);\n    }\n\n    // Split path into parts\n    const pathParts = folderPath.split('/').filter(part => part && part !== '.');\n    let currentPath = '';\n\n    // Create folders sequentially\n    return this.createFoldersSequentially(pathParts, null, currentPath, folderPath);\n  }\n\n  /**\n   * Create folders sequentially in hierarchy\n   */\n  private createFoldersSequentially(\n    pathParts: string[],\n    currentFolderId: number | null,\n    currentPath: string,\n    fullPath: string\n  ): Observable<number | null> {\n    if (pathParts.length === 0) {\n      // Cache the final folder ID\n      if (currentFolderId) {\n        this.folderCache.set(fullPath, currentFolderId);\n      }\n      return of(currentFolderId);\n    }\n\n    const folderName = pathParts[0];\n    const remainingParts = pathParts.slice(1);\n    currentPath = currentPath ? `${currentPath}/${folderName}` : folderName;\n\n    // Check if this partial path is cached\n    if (this.folderCache.has(currentPath)) {\n      const cachedId = this.folderCache.get(currentPath) || null;\n      return this.createFoldersSequentially(remainingParts, cachedId, currentPath, fullPath);\n    }\n\n    // Create or get folder\n    return this.createOrGetFolder(folderName, currentFolderId).pipe(\n      switchMap((folderId: number | null) => {\n        if (folderId === null) {\n          console.warn(`Failed to create folder: ${folderName}`);\n          return of(null);\n        }\n\n        // Cache the folder ID\n        this.folderCache.set(currentPath, folderId);\n\n        // Continue with remaining parts\n        return this.createFoldersSequentially(remainingParts, folderId, currentPath, fullPath);\n      })\n    );\n  }\n\n  /**\n   * Create or get existing folder\n   * @param folderName - Name of the folder\n   * @param parentId - Parent folder ID\n   * @returns Observable with folder ID\n   */\n  private createOrGetFolder(folderName: string, parentId: number | null = null): Observable<number | null> {\n    // First, try to find if folder already exists\n    return this.findFolder(folderName, parentId).pipe(\n      switchMap((existingFolder: any) => {\n        if (existingFolder) {\n          console.log(`Found existing folder: ${folderName} (ID: ${existingFolder.id})`);\n          return of(existingFolder.id);\n        }\n\n        // Create new folder\n        const requestBody: any = { name: folderName };\n        if (parentId) {\n          requestBody.parentId = parentId;\n        }\n\n        const headers = new HttpHeaders({\n          'Content-Type': 'application/json'\n        });\n\n        return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD.replace('/files', '/folders')}`, requestBody, { headers }).pipe(\n          map((response: any) => {\n            if (!response || !response.id) {\n              throw new Error('Invalid response when creating folder');\n            }\n            console.log(`Created folder: ${folderName} (ID: ${response.id})`);\n            return response.id;\n          }),\n          catchError((error: any) => {\n            // Handle folder already exists error\n            if (error.status === 400 || error.status === 500) {\n              return this.findFolder(folderName, parentId).pipe(\n                map((folder: any) => {\n                  if (folder) {\n                    console.log(`Found folder after conflict: ${folderName} (ID: ${folder.id})`);\n                    return folder.id;\n                  }\n                  return null;\n                })\n              );\n            }\n\n            // If media library plugin not available, return null\n            if (error.status === 404 || error.status === 403) {\n              console.warn(`Media library plugin not available or permission denied`);\n              return new Observable(observer => {\n                observer.next(null);\n                observer.complete();\n              });\n            }\n\n            console.error(`Error creating folder ${folderName}: ${error.message}`);\n            return new Observable(observer => {\n              observer.next(null);\n              observer.complete();\n            });\n          })\n        );\n      })\n    );\n  }\n\n  /**\n   * Find existing folder by name and parent\n   * @param folderName - Name of the folder to find\n   * @param parentId - Parent folder ID\n   * @returns Observable with folder data or null\n   */\n  private findFolder(folderName: string, parentId: number | null = null): Observable<any> {\n    const params = new URLSearchParams();\n    if (parentId) {\n      params.append('parentId', parentId.toString());\n    }\n\n    const url = `${CMS_APIContstant.MEDIA_UPLOAD.replace('/files', '/folders')}?${params.toString()}`;\n\n    return this.http.get(url).pipe(\n      map((response: any) => {\n        if (response && Array.isArray(response)) {\n          return response.find((folder: any) => folder.name === folderName);\n        }\n        return null;\n      }),\n      catchError((error: any) => {\n        if (error.status === 404 || error.status === 403) {\n          return new Observable(observer => {\n            observer.next(null);\n            observer.complete();\n          });\n        }\n        console.warn(`Error searching for folder ${folderName}: ${error.message}`);\n        return new Observable(observer => {\n          observer.next(null);\n          observer.complete();\n        });\n      })\n    );\n  }\n\n  /**\n   * Create CRM attachment record\n   * @param uploadResult - Upload result from Strapi\n   * @param activityId - Activity ID for the attachment\n   * @returns Observable with CRM attachment record\n   */\n  private createCRMAttachmentRecord(uploadResult: any, activityId: string): Observable<any> {\n    const attachmentData = {\n      mime_type: uploadResult.mime || uploadResult.mimeType || 'application/octet-stream',\n      name: uploadResult.name || uploadResult.filename,\n      link_web_uri: uploadResult.url,\n      activity_id: activityId\n    };\n\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n\n    return this.http.post(`${CMS_APIContstant.CRM_ATTACHMENTS}`, attachmentData, { headers }).pipe(\n      map((response: any) => {\n        console.log(`✅ CRM attachment record created: ${response.id}`);\n        return response;\n      }),\n      catchError((error: any) => {\n        console.error('Failed to create CRM attachment record:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AACjD,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAC3D,SAASC,gBAAgB,QAAQ,+BAA+B;;;AAKhE,OAAM,MAAOC,kBAAkB;EAI7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHhB,KAAAC,WAAW,GAAG,IAAIC,GAAG,EAAkB;IAC9B,KAAAC,YAAY,GAAG,iBAAiB;EAET;EAExC;;;;;;EAMAC,UAAUA,CAACC,IAAU,EAAEC,IAAA,GAAe,iBAAiB;IACrD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEJ,IAAI,CAAC;IAC9BE,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE7B;IACA,MAAMI,OAAO,GAAG,IAAIpB,WAAW,EAAE;IACjC;IAEA,OAAO,IAAI,CAACU,IAAI,CAACW,IAAI,CAAC,GAAGd,gBAAgB,CAACe,YAAY,EAAE,EAAEL,QAAQ,EAAE;MAAEG;IAAO,CAAE,CAAC;EAClF;EAEA;;;;;;;EAOAG,2BAA2BA,CACzBR,IAAU,EACVS,UAAmB,EACnBC,qBAAA,GAAiC,IAAI;IAErC;IACA,MAAMC,UAAU,GAAG,IAAI,CAACC,YAAY,CAACZ,IAAI,CAAC;IAC1C,IAAI,CAACW,UAAU,CAACE,OAAO,EAAE;MACvB,OAAO1B,UAAU,CAAC,MAAM,IAAI2B,KAAK,CAACH,UAAU,CAACI,KAAK,CAAC,CAAC;IACtD;IAEAC,OAAO,CAACC,GAAG,CAAC,oBAAoBjB,IAAI,CAACkB,IAAI,KAAK,IAAI,CAACC,cAAc,CAACnB,IAAI,CAACoB,IAAI,CAAC,GAAG,CAAC;IAEhF;IACA,MAAMC,gBAAgB,GAAGX,qBAAqB,GAC1C,IAAI,CAACY,kBAAkB,CAAC,IAAI,CAACxB,YAAY,CAAC,GAC1CV,EAAE,CAAC,IAAI,CAAC;IAEZ,OAAOiC,gBAAgB,CAACE,IAAI,CAC1BhC,SAAS,CAAEiC,QAAuB,IAAI;MACpCR,OAAO,CAACC,GAAG,CAAC,qBAAqBO,QAAQ,IAAI,MAAM,EAAE,CAAC;MAEtD;MACA,OAAO,IAAI,CAACC,cAAc,CAACzB,IAAI,EAAEwB,QAAQ,CAAC;IAC5C,CAAC,CAAC,EACFjC,SAAS,CAAEmC,YAAiB,IAAI;MAC9BV,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnCD,OAAO,CAACC,GAAG,CAAC,YAAYjB,IAAI,CAACkB,IAAI,EAAE,CAAC;MACpCF,OAAO,CAACC,GAAG,CAAC,iBAAiBS,YAAY,CAACC,EAAE,EAAE,CAAC;MAC/CX,OAAO,CAACC,GAAG,CAAC,WAAWS,YAAY,CAACE,GAAG,EAAE,CAAC;MAC1CZ,OAAO,CAACC,GAAG,CAAC,cAAc,IAAI,CAACnB,YAAY,EAAE,CAAC;MAE9C;MACA,IAAIW,UAAU,EAAE;QACd,OAAO,IAAI,CAACoB,yBAAyB,CAACH,YAAY,EAAEjB,UAAU,CAAC,CAACc,IAAI,CAClElC,GAAG,CAAEyC,SAAc,KAAM;UACvBC,OAAO,EAAE,IAAI;UACb/B,IAAI,EAAE0B,YAAY;UAClBM,aAAa,EAAEF,SAAS;UACxBG,OAAO,EAAE;SACV,CAAC,CAAC,CACJ;MACH,CAAC,MAAM;QACL,OAAO7C,EAAE,CAAC;UACR2C,OAAO,EAAE,IAAI;UACb/B,IAAI,EAAE0B,YAAY;UAClBO,OAAO,EAAE;SACV,CAAC;MACJ;IACF,CAAC,CAAC,EACF3C,UAAU,CAAEyB,KAAU,IAAI;MACxBC,OAAO,CAACD,KAAK,CAAC,oBAAoBA,KAAK,CAACkB,OAAO,EAAE,CAAC;MAClD,OAAO9C,UAAU,CAAC,OAAO;QACvB4C,OAAO,EAAE,KAAK;QACdhB,KAAK,EAAEA,KAAK,CAACkB,OAAO;QACpBA,OAAO,EAAE;OACV,CAAC,CAAC;IACL,CAAC,CAAC,CACH;EACH;EAEA;;;;;;EAMAC,mBAAmBA,CAACC,KAAa,EAAElC,IAAA,GAAe,iBAAiB;IACjE,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAE/BgC,KAAK,CAACC,OAAO,CAACpC,IAAI,IAAG;MACnBE,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEJ,IAAI,CAAC;IAChC,CAAC,CAAC;IACFE,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE7B,MAAMI,OAAO,GAAG,IAAIpB,WAAW,EAAE;IAEjC,OAAO,IAAI,CAACU,IAAI,CAACW,IAAI,CAAC,GAAGd,gBAAgB,CAACe,YAAY,EAAE,EAAEL,QAAQ,EAAE;MAAEG;IAAO,CAAE,CAAC;EAClF;EAEA;;;;;;EAMAgC,sBAAsBA,CAACrC,IAAU,EAAEC,IAAA,GAAe,iBAAiB;IACjE,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEJ,IAAI,CAAC;IAC9BE,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE7B,OAAO,IAAI,CAACN,IAAI,CAACW,IAAI,CAAC,GAAGd,gBAAgB,CAACe,YAAY,EAAE,EAAEL,QAAQ,EAAE;MAClEoC,cAAc,EAAE,IAAI;MACpBC,OAAO,EAAE;KACV,CAAC;EACJ;EAEA;;;;;EAKAC,UAAUA,CAACC,MAAc;IACvB,OAAO,IAAI,CAAC9C,IAAI,CAAC+C,MAAM,CAAC,GAAGlD,gBAAgB,CAACe,YAAY,IAAIkC,MAAM,EAAE,CAAC;EACvE;EAEA;;;;;EAKAE,cAAcA,CAACF,MAAc;IAC3B,OAAO,IAAI,CAAC9C,IAAI,CAACiD,GAAG,CAAC,GAAGpD,gBAAgB,CAACe,YAAY,IAAIkC,MAAM,EAAE,CAAC;EACpE;EAEA;;;;;EAKAI,cAAcA,CAAC5C,IAAY;IACzB,OAAO,IAAI,CAACN,IAAI,CAACiD,GAAG,CAAC,GAAGpD,gBAAgB,CAACe,YAAY,SAASuC,kBAAkB,CAAC7C,IAAI,CAAC,EAAE,CAAC;EAC3F;EAEA;;;;;;;EAOAW,YAAYA,CACVZ,IAAU,EACV+C,OAAA,GAAkB,EAAE,GAAG,IAAI,GAAG,IAAI;EAAE;EACpCC,YAAuB;IAGvB;IACA,IAAIhD,IAAI,CAACoB,IAAI,GAAG2B,OAAO,EAAE;MACvB,OAAO;QACLlC,OAAO,EAAE,KAAK;QACdE,KAAK,EAAE,sCAAsC,IAAI,CAACI,cAAc,CAAC4B,OAAO,CAAC;OAC1E;IACH;IAEA;IACA,IAAIC,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,IAAI,CAACD,YAAY,CAACE,QAAQ,CAAClD,IAAI,CAACmD,IAAI,CAAC,EAAE;QACrC,OAAO;UACLtC,OAAO,EAAE,KAAK;UACdE,KAAK,EAAE,aAAaf,IAAI,CAACmD,IAAI,mCAAmCH,YAAY,CAACI,IAAI,CAAC,IAAI,CAAC;SACxF;MACH;IACF;IAEA,OAAO;MAAEvC,OAAO,EAAE;IAAI,CAAE;EAC1B;EAEA;;;;;EAKAM,cAAcA,CAACkC,KAAa;IAC1B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/C,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACxC,GAAG,CAACoC,KAAK,CAAC,GAAGI,IAAI,CAACxC,GAAG,CAACqC,CAAC,CAAC,CAAC;IACnD,OAAOK,UAAU,CAAC,CAACN,KAAK,GAAGI,IAAI,CAACG,GAAG,CAACN,CAAC,EAAEE,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGN,KAAK,CAACC,CAAC,CAAC;EACzE;EAEA;;;;;EAKAM,sBAAsBA,CAACC,YAAoB;IACzC,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE;IAC5B,MAAMC,SAAS,GAAGJ,YAAY,CAACK,SAAS,CAACL,YAAY,CAACM,WAAW,CAAC,GAAG,CAAC,CAAC;IACvE,MAAMC,cAAc,GAAGP,YAAY,CAACK,SAAS,CAAC,CAAC,EAAEL,YAAY,CAACM,WAAW,CAAC,GAAG,CAAC,CAAC;IAC/E,OAAO,GAAGC,cAAc,IAAIN,SAAS,GAAGG,SAAS,EAAE;EACrD;EAEA;;;;;;EAMQ1C,cAAcA,CAACzB,IAAU,EAAEwB,QAAA,GAA0B,IAAI;IAC/D,MAAMtB,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEJ,IAAI,EAAEA,IAAI,CAACkB,IAAI,CAAC;IAExC;IACA,IAAIM,QAAQ,EAAE;MACZtB,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEoB,QAAQ,CAAC+C,QAAQ,EAAE,CAAC;IAClD;IAEA,MAAMlE,OAAO,GAAG,IAAIpB,WAAW,EAAE;IACjC;IAEA,OAAO,IAAI,CAACU,IAAI,CAACW,IAAI,CAAC,GAAGd,gBAAgB,CAACe,YAAY,EAAE,EAAEL,QAAQ,EAAE;MAAEG;IAAO,CAAE,CAAC,CAACkB,IAAI,CACnFlC,GAAG,CAAEmF,QAAa,IAAI;MACpB;MACA,IAAIC,YAAY;MAChB,IAAID,QAAQ,IAAIA,QAAQ,CAACE,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACE,IAAI,CAAC,EAAE;QAC7DD,YAAY,GAAGD,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;MACjC,CAAC,MAAM,IAAIF,QAAQ,IAAIG,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;QAC9CC,YAAY,GAAGD,QAAQ,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM,IAAIA,QAAQ,EAAE;QACnBC,YAAY,GAAGD,QAAQ;MACzB,CAAC,MAAM;QACL,MAAM,IAAI1D,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA,IAAI,CAAC2D,YAAY,EAAE;QACjB,MAAM,IAAI3D,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MAEA,OAAO2D,YAAY;IACrB,CAAC,CAAC,EACFnF,UAAU,CAAEyB,KAAU,IAAI;MACxBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO5B,UAAU,CAAC,MAAM4B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;;;;;EAKQO,kBAAkBA,CAACuD,UAAkB;IAC3C;IACA,IAAI,IAAI,CAACjF,WAAW,CAACkF,GAAG,CAACD,UAAU,CAAC,EAAE;MACpC,OAAOzF,EAAE,CAAC,IAAI,CAACQ,WAAW,CAACgD,GAAG,CAACiC,UAAU,CAAC,IAAI,IAAI,CAAC;IACrD;IAEA;IACA,MAAME,SAAS,GAAGF,UAAU,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,IAAIA,IAAI,KAAK,GAAG,CAAC;IAC5E,IAAIC,WAAW,GAAG,EAAE;IAEpB;IACA,OAAO,IAAI,CAACC,yBAAyB,CAACL,SAAS,EAAE,IAAI,EAAEI,WAAW,EAAEN,UAAU,CAAC;EACjF;EAEA;;;EAGQO,yBAAyBA,CAC/BL,SAAmB,EACnBM,eAA8B,EAC9BF,WAAmB,EACnBG,QAAgB;IAEhB,IAAIP,SAAS,CAAC9B,MAAM,KAAK,CAAC,EAAE;MAC1B;MACA,IAAIoC,eAAe,EAAE;QACnB,IAAI,CAACzF,WAAW,CAAC2F,GAAG,CAACD,QAAQ,EAAED,eAAe,CAAC;MACjD;MACA,OAAOjG,EAAE,CAACiG,eAAe,CAAC;IAC5B;IAEA,MAAMG,UAAU,GAAGT,SAAS,CAAC,CAAC,CAAC;IAC/B,MAAMU,cAAc,GAAGV,SAAS,CAACW,KAAK,CAAC,CAAC,CAAC;IACzCP,WAAW,GAAGA,WAAW,GAAG,GAAGA,WAAW,IAAIK,UAAU,EAAE,GAAGA,UAAU;IAEvE;IACA,IAAI,IAAI,CAAC5F,WAAW,CAACkF,GAAG,CAACK,WAAW,CAAC,EAAE;MACrC,MAAMQ,QAAQ,GAAG,IAAI,CAAC/F,WAAW,CAACgD,GAAG,CAACuC,WAAW,CAAC,IAAI,IAAI;MAC1D,OAAO,IAAI,CAACC,yBAAyB,CAACK,cAAc,EAAEE,QAAQ,EAAER,WAAW,EAAEG,QAAQ,CAAC;IACxF;IAEA;IACA,OAAO,IAAI,CAACM,iBAAiB,CAACJ,UAAU,EAAEH,eAAe,CAAC,CAAC9D,IAAI,CAC7DhC,SAAS,CAAEiC,QAAuB,IAAI;MACpC,IAAIA,QAAQ,KAAK,IAAI,EAAE;QACrBR,OAAO,CAAC6E,IAAI,CAAC,4BAA4BL,UAAU,EAAE,CAAC;QACtD,OAAOpG,EAAE,CAAC,IAAI,CAAC;MACjB;MAEA;MACA,IAAI,CAACQ,WAAW,CAAC2F,GAAG,CAACJ,WAAW,EAAE3D,QAAQ,CAAC;MAE3C;MACA,OAAO,IAAI,CAAC4D,yBAAyB,CAACK,cAAc,EAAEjE,QAAQ,EAAE2D,WAAW,EAAEG,QAAQ,CAAC;IACxF,CAAC,CAAC,CACH;EACH;EAEA;;;;;;EAMQM,iBAAiBA,CAACJ,UAAkB,EAAEM,QAAA,GAA0B,IAAI;IAC1E;IACA,OAAO,IAAI,CAACC,UAAU,CAACP,UAAU,EAAEM,QAAQ,CAAC,CAACvE,IAAI,CAC/ChC,SAAS,CAAEyG,cAAmB,IAAI;MAChC,IAAIA,cAAc,EAAE;QAClBhF,OAAO,CAACC,GAAG,CAAC,0BAA0BuE,UAAU,SAASQ,cAAc,CAACrE,EAAE,GAAG,CAAC;QAC9E,OAAOvC,EAAE,CAAC4G,cAAc,CAACrE,EAAE,CAAC;MAC9B;MAEA;MACA,MAAMsE,WAAW,GAAQ;QAAE/E,IAAI,EAAEsE;MAAU,CAAE;MAC7C,IAAIM,QAAQ,EAAE;QACZG,WAAW,CAACH,QAAQ,GAAGA,QAAQ;MACjC;MAEA,MAAMzF,OAAO,GAAG,IAAIpB,WAAW,CAAC;QAC9B,cAAc,EAAE;OACjB,CAAC;MAEF,OAAO,IAAI,CAACU,IAAI,CAACW,IAAI,CAAC,GAAGd,gBAAgB,CAACe,YAAY,CAAC2F,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,EAAED,WAAW,EAAE;QAAE5F;MAAO,CAAE,CAAC,CAACkB,IAAI,CACpHlC,GAAG,CAAEmF,QAAa,IAAI;QACpB,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAAC7C,EAAE,EAAE;UAC7B,MAAM,IAAIb,KAAK,CAAC,uCAAuC,CAAC;QAC1D;QACAE,OAAO,CAACC,GAAG,CAAC,mBAAmBuE,UAAU,SAAShB,QAAQ,CAAC7C,EAAE,GAAG,CAAC;QACjE,OAAO6C,QAAQ,CAAC7C,EAAE;MACpB,CAAC,CAAC,EACFrC,UAAU,CAAEyB,KAAU,IAAI;QACxB;QACA,IAAIA,KAAK,CAACoF,MAAM,KAAK,GAAG,IAAIpF,KAAK,CAACoF,MAAM,KAAK,GAAG,EAAE;UAChD,OAAO,IAAI,CAACJ,UAAU,CAACP,UAAU,EAAEM,QAAQ,CAAC,CAACvE,IAAI,CAC/ClC,GAAG,CAAE+G,MAAW,IAAI;YAClB,IAAIA,MAAM,EAAE;cACVpF,OAAO,CAACC,GAAG,CAAC,gCAAgCuE,UAAU,SAASY,MAAM,CAACzE,EAAE,GAAG,CAAC;cAC5E,OAAOyE,MAAM,CAACzE,EAAE;YAClB;YACA,OAAO,IAAI;UACb,CAAC,CAAC,CACH;QACH;QAEA;QACA,IAAIZ,KAAK,CAACoF,MAAM,KAAK,GAAG,IAAIpF,KAAK,CAACoF,MAAM,KAAK,GAAG,EAAE;UAChDnF,OAAO,CAAC6E,IAAI,CAAC,yDAAyD,CAAC;UACvE,OAAO,IAAI3G,UAAU,CAACmH,QAAQ,IAAG;YAC/BA,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC;YACnBD,QAAQ,CAACE,QAAQ,EAAE;UACrB,CAAC,CAAC;QACJ;QAEAvF,OAAO,CAACD,KAAK,CAAC,yBAAyByE,UAAU,KAAKzE,KAAK,CAACkB,OAAO,EAAE,CAAC;QACtE,OAAO,IAAI/C,UAAU,CAACmH,QAAQ,IAAG;UAC/BA,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC;UACnBD,QAAQ,CAACE,QAAQ,EAAE;QACrB,CAAC,CAAC;MACJ,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEA;;;;;;EAMQR,UAAUA,CAACP,UAAkB,EAAEM,QAAA,GAA0B,IAAI;IACnE,MAAMU,MAAM,GAAG,IAAIC,eAAe,EAAE;IACpC,IAAIX,QAAQ,EAAE;MACZU,MAAM,CAACpG,MAAM,CAAC,UAAU,EAAE0F,QAAQ,CAACvB,QAAQ,EAAE,CAAC;IAChD;IAEA,MAAM3C,GAAG,GAAG,GAAGpC,gBAAgB,CAACe,YAAY,CAAC2F,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAIM,MAAM,CAACjC,QAAQ,EAAE,EAAE;IAEjG,OAAO,IAAI,CAAC5E,IAAI,CAACiD,GAAG,CAAChB,GAAG,CAAC,CAACL,IAAI,CAC5BlC,GAAG,CAAEmF,QAAa,IAAI;MACpB,IAAIA,QAAQ,IAAIG,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;QACvC,OAAOA,QAAQ,CAACkC,IAAI,CAAEN,MAAW,IAAKA,MAAM,CAAClF,IAAI,KAAKsE,UAAU,CAAC;MACnE;MACA,OAAO,IAAI;IACb,CAAC,CAAC,EACFlG,UAAU,CAAEyB,KAAU,IAAI;MACxB,IAAIA,KAAK,CAACoF,MAAM,KAAK,GAAG,IAAIpF,KAAK,CAACoF,MAAM,KAAK,GAAG,EAAE;QAChD,OAAO,IAAIjH,UAAU,CAACmH,QAAQ,IAAG;UAC/BA,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC;UACnBD,QAAQ,CAACE,QAAQ,EAAE;QACrB,CAAC,CAAC;MACJ;MACAvF,OAAO,CAAC6E,IAAI,CAAC,8BAA8BL,UAAU,KAAKzE,KAAK,CAACkB,OAAO,EAAE,CAAC;MAC1E,OAAO,IAAI/C,UAAU,CAACmH,QAAQ,IAAG;QAC/BA,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC;QACnBD,QAAQ,CAACE,QAAQ,EAAE;MACrB,CAAC,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EAEA;;;;;;EAMQ1E,yBAAyBA,CAACH,YAAiB,EAAEjB,UAAkB;IACrE,MAAMkG,cAAc,GAAG;MACrBC,SAAS,EAAElF,YAAY,CAACmF,IAAI,IAAInF,YAAY,CAACoF,QAAQ,IAAI,0BAA0B;MACnF5F,IAAI,EAAEQ,YAAY,CAACR,IAAI,IAAIQ,YAAY,CAACqF,QAAQ;MAChDC,YAAY,EAAEtF,YAAY,CAACE,GAAG;MAC9BqF,WAAW,EAAExG;KACd;IAED,MAAMJ,OAAO,GAAG,IAAIpB,WAAW,CAAC;MAC9B,cAAc,EAAE;KACjB,CAAC;IAEF,OAAO,IAAI,CAACU,IAAI,CAACW,IAAI,CAAC,GAAGd,gBAAgB,CAAC0H,eAAe,EAAE,EAAEP,cAAc,EAAE;MAAEtG;IAAO,CAAE,CAAC,CAACkB,IAAI,CAC5FlC,GAAG,CAAEmF,QAAa,IAAI;MACpBxD,OAAO,CAACC,GAAG,CAAC,oCAAoCuD,QAAQ,CAAC7C,EAAE,EAAE,CAAC;MAC9D,OAAO6C,QAAQ;IACjB,CAAC,CAAC,EACFlF,UAAU,CAAEyB,KAAU,IAAI;MACxBC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,OAAO5B,UAAU,CAAC,MAAM4B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;;;uBAtcWtB,kBAAkB,EAAA0H,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlB7H,kBAAkB;MAAA8H,OAAA,EAAlB9H,kBAAkB,CAAA+H,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}