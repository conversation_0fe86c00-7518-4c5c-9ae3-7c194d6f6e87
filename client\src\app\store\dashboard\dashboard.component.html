<div class="col-12 all-overview-body m-0 p-0 border-round-lg surface-card">
    <!-- <img src="assets/layout/images/home-page-dashboard.png" class="w-full" alt="" /> -->
    <div class="grid mt-0">
        <div class="col-12 lg:col-4 md:col-4 sm:col-6 xs:col-12">
            <div
                class="flex w-full shadow-1 border-round-xl surface-0 p-4 border-1 border-solid border-50 justify-content-between">
                <div class="d-chart-info flex flex-column">
                    <h3 class="m-0 mb-2 text-xl">Open Flex Calls</h3>
                    <h4 class="m-0 text-4xl text-orange-500 font-bold">1.25K</h4>
                    <p class="flex gap-2 m-0 mt-auto text-lg font-medium text-600">
                        <span class="flex gap-2 text-green-500">
                            <i class="material-symbols-rounded">trending_up</i> 5.75%
                        </span> This Week
                    </p>
                </div>
                <div class="d-chart-chart">
                    <video width="180" height="180" autoplay muted loop>
                        <source src="assets/layout/videos/flex-calls.mp4" type="video/mp4" />
                    </video>
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6 xs:col-12">
            <div
                class="flex w-full shadow-1 border-round-xl surface-0 p-4 border-1 border-solid border-50 justify-content-between">
                <div class="d-chart-info flex flex-column">
                    <h3 class="m-0 mb-2 text-xl">Completed Activities</h3>
                    <h4 class="m-0 text-4xl text-orange-500 font-bold">1.25K</h4>
                    <p class="flex gap-2 m-0 mt-auto text-lg font-medium text-600">
                        <span class="flex gap-2 text-green-500">
                            <i class="material-symbols-rounded">trending_up</i> 5.75%
                        </span> This Week
                    </p>
                </div>
                <div class="d-chart-chart">
                    <video width="180" height="180" autoplay muted loop>
                        <source src="assets/layout/videos/activities-chart.mp4" type="video/mp4" />
                    </video>
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6 xs:col-12">
            <div
                class="flex w-full shadow-1 border-round-xl surface-0 p-4 border-1 border-solid border-50 justify-content-between">
                <div class="d-chart-info flex flex-column">
                    <h3 class="m-0 mb-2 text-xl">Completed Connections</h3>
                    <h4 class="m-0 text-4xl text-green-500 font-bold">800</h4>
                    <p class="flex gap-2 m-0 mt-auto text-lg font-medium text-600">
                        <span class="flex gap-2 text-red-500">
                            <i class="material-symbols-rounded">trending_down</i> 7.75%
                        </span> This Week
                    </p>
                </div>
                <div class="d-chart-chart">
                    <video width="180" height="180" autoplay muted loop>
                        <source src="assets/layout/videos/connections-chart.mp4" type="video/mp4" />
                    </video>
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6 xs:col-12">
            <div
                class="flex w-full shadow-1 border-round-xl surface-0 p-4 border-1 border-solid border-50 justify-content-between">
                <div class="d-chart-info flex flex-column">
                    <h3 class="m-0 mb-2 text-xl">Flexible Call Campaign</h3>
                    <h4 class="m-0 text-4xl text-green-500 font-bold">800</h4>
                    <p class="flex gap-2 m-0 mt-auto text-lg font-medium text-600">
                        <span class="flex gap-2 text-red-500">
                            <i class="material-symbols-rounded">trending_down</i> 7.75%
                        </span> This Week
                    </p>
                </div>
                <div class="d-chart-chart">
                    <video width="180" height="180" autoplay muted loop>
                        <source src="assets/layout/videos/campaign-analysis.mp4" type="video/mp4" />
                    </video>
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6 xs:col-12">
            <div
                class="flex w-full shadow-1 border-round-xl surface-0 p-4 border-1 border-solid border-50 justify-content-between">
                <div class="d-chart-info flex flex-column">
                    <h3 class="m-0 mb-2 text-xl">Past Due Opportunities</h3>
                    <h4 class="m-0 text-4xl text-green-500 font-bold">800</h4>
                    <p class="flex gap-2 m-0 mt-auto text-lg font-medium text-600">
                        <span class="flex gap-2 text-red-500">
                            <i class="material-symbols-rounded">trending_down</i> 7.75%
                        </span> This Week
                    </p>
                </div>
                <div class="d-chart-chart">
                    <video width="180" height="180" autoplay muted loop>
                        <source src="assets/layout/videos/opportunities.mp4" type="video/mp4" />
                    </video>
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6 xs:col-12">
            <div
                class="flex w-full shadow-1 border-round-xl surface-0 p-4 border-1 border-solid border-50 justify-content-between">
                <div class="d-chart-info flex flex-column">
                    <h3 class="m-0 mb-2 text-xl">No Call in 45 Days</h3>
                    <h4 class="m-0 text-4xl text-green-500 font-bold">800</h4>
                    <p class="flex gap-2 m-0 mt-auto text-lg font-medium text-600">
                        <span class="flex gap-2 text-red-500">
                            <i class="material-symbols-rounded">trending_down</i> 7.75%
                        </span> This Week
                    </p>
                </div>
                <div class="d-chart-chart">
                    <video width="180" height="180" autoplay muted loop>
                        <source src="assets/layout/videos/no-calls.mp4" type="video/mp4" />
                    </video>
                </div>
            </div>
        </div>

    </div>

    <div class="grid mt-0">
        <div class="col-12 lg:col-6 md:col-6 sm:col-6 xs:col-12">
            <div class="mt-1 w-full shadow-1 border-round-xl surface-0 p-4 pb-0 border-1 border-solid border-50">

                <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2">
                    <h4 class="m-0 pl-3 left-border relative flex">All Tickets</h4>

                    <div class="flex align-items-center gap-3">
                        <p-multiSelect [options]="ActivitiesCols" [(ngModel)]="selectedActivitiesColumns"
                            optionLabel="header" class="table-multiselect-dropdown"
                            [styleClass]="'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'">
                        </p-multiSelect>
                    </div>
                </div>

                <div class="table-sec">
                    <p-table [value]="tableData" dataKey="id" [rows]="8" styleClass="" [paginator]="true"
                        [scrollable]="true" [reorderableColumns]="true"
                        (onColReorder)="onActivitiesColumnReorder($event)" responsiveLayout="scroll"
                        class="scrollable-table">

                        <ng-template pTemplate="header">
                            <tr>
                                <th pFrozenColumn class="border-round-left-lg"
                                    (click)="customSort('ticket_no', tableData, 'activities')">
                                    <div class="flex align-items-center cursor-pointer">
                                        Ticket #
                                        <i *ngIf="sortFieldActivities === 'ticket_no'" class="ml-2 pi"
                                            [ngClass]="sortOrderActivities === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                                        <i *ngIf="sortFieldActivities !== 'ticket_no'" class="ml-2 pi pi-sort"></i>
                                    </div>
                                </th>

                                <ng-container *ngFor="let col of selectedActivitiesColumns">
                                    <th [pSortableColumn]="col.field" pReorderableColumn
                                        (click)="customSort(col.field, tableData, 'activities')">
                                        <div class="flex align-items-center cursor-pointer">
                                            {{ col.header }}
                                            <i *ngIf="sortFieldActivities === col.field" class="ml-2 pi"
                                                [ngClass]="sortOrderActivities === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                                            <i *ngIf="sortFieldActivities !== col.field" class="ml-2 pi pi-sort"></i>
                                        </div>
                                    </th>
                                </ng-container>
                                <th class="border-round-right-lg"
                                    (click)="customSort('status', tableData, 'activities')">
                                    <div class="flex align-items-center cursor-pointer">
                                        Status
                                        <i *ngIf="sortFieldActivities === 'status'" class="ml-2 pi"
                                            [ngClass]="sortOrderActivities === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                                        <i *ngIf="sortFieldActivities !== 'status'" class="ml-2 pi pi-sort"></i>
                                    </div>
                                </th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-tableinfo>
                            <tr class="cursor-pointer">
                                <td pFrozenColumn
                                    class="text-orange-600 cursor-pointer font-medium underline border-round-left-lg">
                                    <div [routerLink]="'/store/sales-quotes/overview'">
                                        {{ tableinfo.ticket_no }}
                                    </div>
                                </td>
                                <ng-container *ngFor="let col of selectedActivitiesColumns">
                                    <td>
                                        <ng-container [ngSwitch]="col.field">
                                            <ng-container *ngSwitchCase="'account_no'">
                                                {{ tableinfo.account_no }}
                                            </ng-container>

                                            <ng-container *ngSwitchCase="'contact_no'">
                                                {{ tableinfo.contact_no }}
                                            </ng-container>

                                            <ng-container *ngSwitchCase="'assign_to'">
                                                {{ tableinfo.assign_to }}
                                            </ng-container>

                                            <ng-container *ngSwitchCase="'created_at'">
                                                {{ tableinfo.created_at }}
                                            </ng-container>

                                        </ng-container>
                                    </td>
                                </ng-container>
                                <td class="border-round-right-lg">
                                    {{ tableinfo.status }}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>

        <div class="col-12 lg:col-6 md:col-6 sm:col-6 xs:col-12">
            <div class="mt-1 w-full shadow-1 border-round-xl surface-0 p-4 pb-0 border-1 border-solid border-50">

                <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-between gap-2">
                    <h4 class="m-0 pl-3 left-border relative flex">All Tickets</h4>

                    <div class="flex align-items-center gap-3">
                        <p-multiSelect [options]="ActivitiesTaskCols" [(ngModel)]="selectedActivitiesTaskColumns"
                            optionLabel="header" class="table-multiselect-dropdown"
                            [styleClass]="'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'">
                        </p-multiSelect>
                    </div>
                </div>

                <div class="table-sec">
                    <p-table [value]="secTableData" dataKey="id" [rows]="8" styleClass="" [paginator]="true"
                        [scrollable]="true" [reorderableColumns]="true" 
                        (onColReorder)="onActivitiesTaskColumnReorder($event)" responsiveLayout="scroll"
                        class="scrollable-table">

                        <ng-template pTemplate="header">
                            <tr>
                                <th pFrozenColumn class="border-round-left-lg"
                                    (click)="customSort('ticket_no', secTableData, 'task')">
                                    <div class="flex align-items-center cursor-pointer">
                                        Ticket #
                                        <i *ngIf="sortFieldActivitiesTask === 'ticket_no'" class="ml-2 pi"
                                            [ngClass]="sortOrderActivitiesTask === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                                        <i *ngIf="sortFieldActivitiesTask !== 'ticket_no'" class="ml-2 pi pi-sort"></i>
                                    </div>
                                </th>

                                <ng-container *ngFor="let col of selectedActivitiesTaskColumns">
                                    <th [pSortableColumn]="col.field" pReorderableColumn
                                        (click)="customSort(col.field, secTableData, 'task')">
                                        <div class="flex align-items-center cursor-pointer">
                                            {{ col.header }}
                                            <i *ngIf="sortFieldActivitiesTask === col.field" class="ml-2 pi"
                                                [ngClass]="sortOrderActivitiesTask === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                                            <i *ngIf="sortFieldActivitiesTask !== col.field"
                                                class="ml-2 pi pi-sort"></i>
                                        </div>
                                    </th>
                                </ng-container>
                                <th class="border-round-right-lg"
                                    (click)="customSort('status', secTableData, 'task')">
                                    <div class="flex align-items-center cursor-pointer">
                                        Status
                                        <i *ngIf="sortFieldActivities === 'status'" class="ml-2 pi"
                                            [ngClass]="sortOrderActivities === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'"></i>
                                        <i *ngIf="sortFieldActivities !== 'status'" class="ml-2 pi pi-sort"></i>
                                    </div>
                                </th>
                            </tr>
                        </ng-template>

                        <ng-template pTemplate="body" let-tableinfo>
                            <tr class="cursor-pointer">
                                <td pFrozenColumn
                                    class="text-orange-600 cursor-pointer font-medium underline border-round-left-lg">
                                    <div [routerLink]="'/store/sales-quotes/overview'">
                                        {{ tableinfo.ticket_no }}
                                    </div>
                                </td>
                                <ng-container *ngFor="let col of selectedActivitiesTaskColumns">
                                    <td>
                                        <ng-container [ngSwitch]="col.field">
                                            <ng-container *ngSwitchCase="'account_no'">
                                                {{ tableinfo.account_no }}
                                            </ng-container>

                                            <ng-container *ngSwitchCase="'contact_no'">
                                                {{ tableinfo.contact_no }}
                                            </ng-container>

                                            <ng-container *ngSwitchCase="'assign_to'">
                                                {{ tableinfo.assign_to }}
                                            </ng-container>

                                            <ng-container *ngSwitchCase="'created_at'">
                                                {{ tableinfo.created_at }}
                                            </ng-container>

                                        </ng-container>
                                    </td>
                                </ng-container>
                                <td class="border-round-right-lg">
                                    {{ tableinfo.status }}
                                </td>
                            </tr>
                        </ng-template>
                    </p-table>
                </div>
            </div>
        </div>
    </div>

</div>