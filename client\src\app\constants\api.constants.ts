import { environment } from '../../environments/environment';

export const ENDPOINT = {
  NODE: environment.apiEndpoint,
  CMS: environment.cmsApiEndpoint,
};

export const ApiConstant = {
  FETCH_TOKEN: `${environment.apiEndpoint}/api/auth/token`,
  ORDER_HISTORY: `${environment.apiEndpoint}/orders`,
  GET_ALL_PRODUCTS: `${environment.apiEndpoint}/products`,
  GET_PRODUCT_IMAGES: `${environment.apiEndpoint}/products/{id}/media`,
  PRODUCT_IMAGE: `${environment.apiEndpoint}/media`,
  PRODUCT_PLANT: `${environment.apiEndpoint}/product-plants`,
  PRODUCT_DESCRIPTION: `${environment.apiEndpoint}/product-descriptions`,
  USERS: `${environment.apiEndpoint}/users`,
  ADMIN_USERS: `${environment.apiEndpoint}/api/admin-users`,
  SINGOUT: `${environment.apiEndpoint}/auth/logout`,
  PARTNERS: `${environment.apiEndpoint}/api/business-partners`,
  CUSTOMER_COMPANIES: `${environment.apiEndpoint}/customer-companies`,
  CUSTOMER_TEXT: `${environment.apiEndpoint}/customer-text`,
  CUSTOMER_PARTNER_FUNCTION: `${environment.apiEndpoint}/customer-partner-functions`,
  CUSTOMER_SALES_AREA: `${environment.apiEndpoint}/customer-sales-areas`,
  GET_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,
  GET_INVOICE_FORM_TYPES: `${environment.apiEndpoint}/invoice-form-types`,
  RELATIONSHIP_TYPES: `${environment.apiEndpoint}/relationship-types`,
  CONDITIONS: `${environment.apiEndpoint}/conditions`,
  GET_CATALOGS: `${environment.apiEndpoint}/product-catalogs`,
  PRODUCT_CATEGORIES: `${environment.apiEndpoint}/products/{product_id}/categories`,
  PRODUCT_CATALOGS: `${environment.apiEndpoint}/products/{product_id}/catalogs`,
  GET_INVOICE_STATUSES: `${environment.apiEndpoint}/invoice-statuses`,
  GET_SALE_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,
  GET_INVOICE_TYPES: `${environment.apiEndpoint}/invoice-types`,
  GET_ORDER_TYPES: `${environment.apiEndpoint}/order-types`,
  ORDER_DETAILS: `${environment.apiEndpoint}/orders`,
  SCHEDULED_ORDER_DETAILS: `${environment.apiEndpoint}/sales-orders-scheduler`,
  INVOICE: `${environment.apiEndpoint}/api/invoices`,
  CREDIT_MEMO: `${environment.apiEndpoint}/api/credit-memos/vendor`,
  IMAGES: `${environment.apiEndpoint}/media`,
  SALES_ORDER_SIMULATION: `${environment.apiEndpoint}/sales-order/simulation`,
  SALES_ORDER_CREATION: `${environment.apiEndpoint}/sales-order/creation`,
  SALES_ORDER: `${environment.apiEndpoint}/api/sales-orders`,
  SALES_ORDER_GENERIC: `${environment.apiEndpoint}/api/sales-orders/generic`,
  ADD_SHIPPING_ADDRESS: `${environment.apiEndpoint}/sales-order/add-shipping-address`,
  CARTS: `${environment.apiEndpoint}/carts`,
  GET_MATERIAL_STOCK: `${environment.apiEndpoint}/products/{id}/material-stock`,
  GET_SALES_PRICE: `${environment.apiEndpoint}/sales-price`,
  CUSTOMERS: `${environment.apiEndpoint}/customers`,
  SETTINGS: `${environment.apiEndpoint}/settings`,
  COMPANY_LOGO: `${environment.apiEndpoint}/company-logo`,
  SIMILAR_PRODUCTS: `${environment.apiEndpoint}/similar-products`,
  TICKET_LIST: `${environment.apiEndpoint}/tickets/list`,
  TICKETS: `${environment.apiEndpoint}/tickets`,
  TICKET_STATUSES: `${environment.apiEndpoint}/ticket-statuses`,
  SALES_QUOTE_CREATION: `${environment.apiEndpoint}/sales-quote/create`,
  SALES_QUOTE: `${environment.apiEndpoint}/api/sales-quotes`,
  SALES_QUOTE_GENERIC: `${environment.apiEndpoint}/api/sales-quotes/generic`,
  QUOTE: `${environment.apiEndpoint}/quote-statuses`,
  RETURN_STATUS: `${environment.apiEndpoint}/return-statuses`,
  RETURN_REASON: `${environment.apiEndpoint}/return-reason`,
  REFUND_PROGRESS: `${environment.apiEndpoint}/refund-progress`,
  RETURN_ORDER: `${environment.apiEndpoint}/api/return-orders`,
  NOTIFICATION: `${environment.apiEndpoint}/notification`,
  PRODUCT_REGISTER: `${environment.apiEndpoint}/product-register`,
  BULK_UPLOAD_STATUS: `${environment.apiEndpoint}/media-upload-status`,
  CUSTOMER_TEXT_DESCR: `${environment.apiEndpoint}/customer-text-description`,
  SALES_ORDER_SCHEDULER_CREATION: `${environment.apiEndpoint}/sales-orders-scheduler`,
  SALES_ORDER_SCHEDULER: `${environment.apiEndpoint}/sales-orders-scheduler`,
  BANNER: `${environment.apiEndpoint}/banner`,
  BUSINESS_PARTNER: `${environment.apiEndpoint}/api/business-partners`,
};

export const CMS_APIContstant = {
  USER_ROLES: `${environment.cmsApiEndpoint}/api/users-permissions/roles`,
  STORE_DESIGN: `${environment.cmsApiEndpoint}/api/store-design?pLevel=6`,
  CUSTOMER_PARTNER_FUNCTION: `${environment.cmsApiEndpoint}/api/customer-partner-functions`,
  TICKET: `${environment.cmsApiEndpoint}/api/tickets`,
  SETTINGS: `${environment.cmsApiEndpoint}/api/settings`,
  FILE_UPLOAD: `${environment.cmsApiEndpoint}/api/import-file-states`,
  FILE_EXPORT: `${environment.cmsApiEndpoint}/api/export/import-file-logs`,
  MAIN_MENU_API_DETAILS: `${environment.cmsApiEndpoint}/api/menu?pLevel=5`,
  PARTNERS: `${environment.cmsApiEndpoint}/api/business-partners`,
  PARTNERS_ADDRESS: `${environment.cmsApiEndpoint}/api/business-partner-addresses`,
  PARTNERS_CONTACTS: `${environment.cmsApiEndpoint}/api/business-partner-contacts`,
  SINGIN: `${environment.cmsApiEndpoint}/api/auth/local`,
  USER_DETAILS: `${environment.cmsApiEndpoint}/api/users`,
  USER_CART: `${environment.cmsApiEndpoint}/api/carts`,
  RESET_PASSWORD_REQUEST: `${environment.cmsApiEndpoint}/api/auth/forgot-password`,
  RESET_PASSWORD: `${environment.cmsApiEndpoint}/api/auth/reset-password`,
  CUSTOMERS: `${environment.cmsApiEndpoint}/api/customers`,
  USER_PERMISSIONS: `${environment.cmsApiEndpoint}/api/storefront-permissions`,
  GET_CATEGORIES: `${environment.cmsApiEndpoint}/api/product-categories`,
  CONFIG_DATA: `${environment.cmsApiEndpoint}/api/configurations`,
  PRODUCT_MDEIA: `${environment.cmsApiEndpoint}/api/product-medias`,
  ACCOUNT_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contacts`,
  PROSPECTS: `${environment.cmsApiEndpoint}/api/business-partner`,
  REGISTER_PROSPECTS: `${environment.cmsApiEndpoint}/api/business-partner/registration`,
  PROSPECT_ADDRESS_REGISTER: `${environment.cmsApiEndpoint}/api/business-partner-address/registration`,
  PROSPECT_ADDRESS: `${environment.cmsApiEndpoint}/api/business-partner-address`,
  CREATE_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact/registration`,
  EXISTING_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact/mapping`,
  PROSPECT_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contact`,
  CRM_NOTE: `${environment.cmsApiEndpoint}/api/crm-notes`,
  CRM_ACTIVITY: `${environment.cmsApiEndpoint}/api/crm-activities`,
  CRM_ACTIVITY_PHONE_CALL_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-activity/registration/phone-call`,
  CRM_ACTIVITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-follow-up-and-related-item/registration`,
  CRM_ACTIVITY_TASK_REGISTRATION:`${environment.cmsApiEndpoint}/api/crm-activity/registration/task`,
  CRM_OPPORTUNITY_ACTIVITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-document/activity-registration`,
  CRM_INVOLVED_PARTIES: `${environment.cmsApiEndpoint}/api/crm-involved-parties`,
  CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS: `${environment.cmsApiEndpoint}/api/crm-follow-up-and-related-items`,
  CRM_OPPORTUNITY_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity/registration`,
  CRM_OPPORTUNITY_FOLLOWUP: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-documents`,
  CRM_OPPORTUNITY_FOLLOW_UP_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-preceding-and-follow-up-document/opportunity-registration`,
  CRM_OPPORTUNITY_CONTACT: `${environment.cmsApiEndpoint}/api/crm-opportunity-party-contact-parties`,
  CRM_OPPORTUNITY_CONTACT_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-opportunity-party-contact-party/registration`,
  CRM_OPPORTUNITY: `${environment.cmsApiEndpoint}/api/crm-opportunities`,
  CRM_OPPORTUNITY_SALES_TEAM: `${environment.cmsApiEndpoint}/api/crm-opportunity-sales-team-parties`,
  CRM_ORGANIZATIONAL_REGISTRATION: `${environment.cmsApiEndpoint}/api/crm-organisational-unit/registration`,
  CRM_ORGANIZATIONAL: `${environment.cmsApiEndpoint}/api/crm-organisational-units`,
  CRM_ORGANIZATIONAL_FUNCTIONS: `${environment.cmsApiEndpoint}/api/crm-organisational-unit-functions`,
  CRM_ORGANIZATIONAL_EMPLOYEES: `${environment.cmsApiEndpoint}/api/crm-organisational-unit-employees`,
  CRM_ORGANIZATIONAL_MANAGERS: `${environment.cmsApiEndpoint}/api/crm-organisational-unit-managers`,
  CRM_ORGANIZATIONAL_ADDRESS: `${environment.cmsApiEndpoint}/api/crm-organisational-unit-addresses`,
  CRM_COMPETITORS: `${environment.cmsApiEndpoint}/api/crm-competitors`,
  CRM_ATTACHMENTS: `${environment.cmsApiEndpoint}/api/crm-attachments`,
  MEDIA_UPLOAD: `${environment.cmsApiEndpoint}/api/media/files`,
  REGISTER_PROSPECT_SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-function/registration`,
  SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-function`,
  BP_EXTENSIONS: `${environment.cmsApiEndpoint}/api/business-partner-extensions`,
  DELETE_SALES_TEAM: `${environment.cmsApiEndpoint}/api/customer-partner-functions`,
  MARKETING_ATTRIBUTES: `${environment.cmsApiEndpoint}/api/bp-marketing-attributes`,
  CONTENT_CRM: `${environment.cmsApiEndpoint}/api/content-crms`,
  EXPORT_URL:`${environment.cmsApiEndpoint}/api/export/`,
};

export const AppConstant = {
  SESSION_TIMEOUT: 3600 * 1000, // in MS
  PRODUCT_IMAGE_FALLBACK: 'assets/layout/images/demo-product.jpeg',
};

export const Permission = {
  VIEW_INVOICE: 'P0003',
};
