{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nexport class ActivitiesService {\n  constructor(http, authservice) {\n    this.http = http;\n    this.authservice = authservice;\n    this.activitySubject = new BehaviorSubject(null);\n    this.activity = this.activitySubject.asObservable();\n  }\n  createNote(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\n      data\n    });\n  }\n  createActivity(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_ACTIVITY_PHONE_CALL_REGISTRATION}`, data);\n  }\n  createActivityTask(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_ACTIVITY_TASK_REGISTRATION}`, data);\n  }\n  createFollowup(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_ACTIVITY_FOLLOW_UP_REGISTRATION}`, data);\n  }\n  createOpportuniyFollowup(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_ACTIVITY_FOLLOW_UP_REGISTRATION}`, data);\n  }\n  createInvolvedParty(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_INVOLVED_PARTIES}`, {\n      data\n    });\n  }\n  createRelatedItem(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS}`, {\n      data\n    });\n  }\n  updateActivity(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, {\n      data\n    });\n  }\n  updateNote(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\n      data\n    });\n  }\n  updateActivityStatus(Id, data) {\n    return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, {\n      data\n    });\n  }\n  deleteNote(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_NOTE}/${id}`);\n  }\n  deleteInvolvedParty(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_INVOLVED_PARTIES}/${id}`);\n  }\n  deleteFollowupItem(id) {\n    return this.http.delete(`${CMS_APIContstant.CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS}/${id}`);\n  }\n  getActivityDropdownOptions(type) {\n    const params = new HttpParams().set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', type);\n    return this.http.get(`${CMS_APIContstant.CONFIG_DATA}`, {\n      params\n    });\n  }\n  getActivities(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'main_account_party_id,subject,activity_status,start_date,end_date,category');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][main_account_party_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\n      params = params.set('filters[$or][2][category][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    });\n  }\n  getSalesCall(page, pageSize, sortField, sortOrder, searchTerm, filter) {\n    let params = new HttpParams().set('filters[document_type][$eq]', '0002').set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'main_account_party_id,activity_id,subject,owner_party_id,brand,customer_group,ranking,activity_status,phone_call_category,customer_timezone,createdAt,updatedAt').set('populate[notes][fields][0]', 'note').set('populate[notes][fields][1]', 'is_global_note').set('populate[business_partner][populate][addresses][fields][0]', 'region').set('populate[business_partner][populate][addresses][fields][1]', 'country').set('populate[business_partner][fields][0]', 'bp_full_name').set('populate[business_partner_owner][fields][0]', 'bp_full_name');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    } else {\n      // Default sort by updatedAt descending\n      params = params.set('sort', 'updatedAt:desc');\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][main_account_party_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\n      params = params.set('filters[$or][2][phone_call_category][$containsi]', searchTerm);\n      params = params.set('filters[$or][3][brand][$containsi]', searchTerm);\n      params = params.set('filters[$or][4][business_partner][bp_full_name][$containsi]', searchTerm);\n      params = params.set('filters[$or][5][ranking][$containsi]', searchTerm);\n      params = params.set('filters[$or][6][business_partner_owner][bp_full_name][$containsi]', searchTerm);\n      params = params.set('filters[$or][7][customer_group][$containsi]', searchTerm);\n    }\n    if (filter) {\n      if (filter === 'MSCT') {\n        const today = new Date();\n        const startOfDay = new Date(today.setHours(0, 0, 0, 0)).toISOString();\n        const endOfDay = new Date(today.setHours(23, 59, 59, 999)).toISOString();\n        params = params.set('filters[createdAt][$gte]', startOfDay).set('filters[createdAt][$lte]', endOfDay);\n      } else if (filter === 'MSCTW') {\n        const now = new Date();\n        // Get the start of the week (Monday)\n        const startOfWeek = new Date(now);\n        const day = now.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6\n        const diffToMonday = day === 0 ? -6 : 1 - day;\n        startOfWeek.setDate(now.getDate() + diffToMonday);\n        startOfWeek.setHours(0, 0, 0, 0);\n        // Get the end of the week (Sunday)\n        const endOfWeek = new Date(startOfWeek);\n        endOfWeek.setDate(startOfWeek.getDate() + 6);\n        endOfWeek.setHours(23, 59, 59, 999);\n        const startISO = startOfWeek.toISOString();\n        const endISO = endOfWeek.toISOString();\n        params = params.set('filters[createdAt][$gte]', startISO).set('filters[createdAt][$lte]', endISO);\n      } else if (filter === 'MSCTM') {\n        const now = new Date();\n        // Start of the month: 1st day at 00:00:00\n        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);\n        // End of the month: last day at 23:59:59.999\n        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);\n        const startISO = startOfMonth.toISOString();\n        const endISO = endOfMonth.toISOString();\n        params = params.set('filters[createdAt][$gte]', startISO).set('filters[createdAt][$lte]', endISO);\n      } else if (filter === 'MCSC') {\n        params = params.set('filters[activity_status][$eq]', '3');\n      } else if (filter === 'MSC') {\n        const email = this.authservice.getUserEmail();\n        if (email) {\n          params = params.set(`filters[$and][0][business_partner][customer][partner_functions][partner_function][$eq]`, 'YI').set(`filters[$and][1][business_partner][customer][partner_functions][business_partner][addresses][emails][email_address][$containsi]`, email);\n        }\n      }\n    }\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    });\n  }\n  getTasks(page, pageSize, sortField, sortOrder, searchTerm, filter) {\n    let params = new HttpParams().set('filters[document_type][$eq]', '0006').set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString()).set('fields', 'main_account_party_id,activity_id,subject,owner_party_id,activity_status,priority,processor_party_id,task_category,start_date,end_date').set('populate[notes][fields][0]', 'note').set('populate[notes][fields][1]', 'is_global_note').set('populate[business_partner][populate][addresses][fields][0]', 'region').set('populate[business_partner][populate][addresses][fields][1]', 'country').set('populate[business_partner][fields][0]', 'bp_full_name').set('populate[business_partner_contact][fields][0]', 'bp_full_name').set('populate[business_partner_processor][fields][0]', 'bp_full_name').set('populate[business_partner_owner][fields][0]', 'bp_full_name');\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      params = params.set('sort', `${sortField}:${order}`);\n    } else {\n      // Default sort by updatedAt descending\n      params = params.set('sort', 'updatedAt:desc');\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][main_account_party_id][$containsi]', searchTerm);\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\n      params = params.set('filters[$or][2][task_category][$containsi]', searchTerm);\n      params = params.set('filters[$or][3][business_partner][bp_full_name][$containsi]', searchTerm);\n      params = params.set('filters[$or][4][business_partner_contact][bp_full_name][$containsi]', searchTerm);\n      params = params.set('filters[$or][5][business_partner_owner][bp_full_name][$containsi]', searchTerm);\n      params = params.set('filters[$or][6][business_partner_processor][bp_full_name][$containsi]', searchTerm);\n    }\n    if (filter) {\n      if (filter === 'MTT') {\n        const today = new Date();\n        const startOfDay = new Date(today.setHours(0, 0, 0, 0)).toISOString();\n        const endOfDay = new Date(today.setHours(23, 59, 59, 999)).toISOString();\n        params = params.set('filters[createdAt][$gte]', startOfDay).set('filters[createdAt][$lte]', endOfDay);\n      } else if (filter === 'MTTW') {\n        const now = new Date();\n        // Get the start of the week (Monday)\n        const startOfWeek = new Date(now);\n        const day = now.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6\n        const diffToMonday = day === 0 ? -6 : 1 - day;\n        startOfWeek.setDate(now.getDate() + diffToMonday);\n        startOfWeek.setHours(0, 0, 0, 0);\n        // Get the end of the week (Sunday)\n        const endOfWeek = new Date(startOfWeek);\n        endOfWeek.setDate(startOfWeek.getDate() + 6);\n        endOfWeek.setHours(23, 59, 59, 999);\n        const startISO = startOfWeek.toISOString();\n        const endISO = endOfWeek.toISOString();\n        params = params.set('filters[createdAt][$gte]', startISO).set('filters[createdAt][$lte]', endISO);\n      } else if (filter === 'MTTM') {\n        const now = new Date();\n        // Start of the month: 1st day at 00:00:00\n        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);\n        // End of the month: last day at 23:59:59.999\n        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);\n        const startISO = startOfMonth.toISOString();\n        const endISO = endOfMonth.toISOString();\n        params = params.set('filters[createdAt][$gte]', startISO).set('filters[createdAt][$lte]', endISO);\n      } else if (filter === 'MCT') {\n        params = params.set('filters[activity_status][$eq]', '3');\n      } else if (filter === 'MOT') {\n        params = params.set('filters[activity_status][$eq]', '1');\n      } else if (filter === 'MTST') {\n        // params = params.set('filters[activity_status][$eq]', '1');\n      } else if (filter === 'MT') {\n        const email = this.authservice.getUserEmail();\n        if (email) {\n          params = params.set(`filters[$and][0][business_partner][customer][partner_functions][partner_function][$eq]`, 'YI').set(`filters[$and][1][business_partner][customer][partner_functions][business_partner][addresses][emails][email_address][$containsi]`, email);\n        }\n      }\n    }\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    });\n  }\n  getActivityByID(activityId) {\n    const params = new HttpParams().set('filters[activity_id][$eq]', activityId).set('populate[business_partner][fields][0]', 'bp_full_name').set('populate[business_partner][fields][1]', 'bp_id').set('populate[business_partner][populate][customer][populate][partner_functions][fields][0]', 'partner_function').set('populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][0]', 'first_name').set('populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][1]', 'last_name').set('populate[business_partner][populate][addresses][populate][address_usages][fields][0]', 'address_usage').set('populate[business_partner][populate][addresses][fields][0]', 'house_number').set('populate[business_partner][populate][addresses][fields][1]', 'street_name').set('populate[business_partner][populate][addresses][fields][2]', 'city_name').set('populate[business_partner][populate][addresses][fields][3]', 'region').set('populate[business_partner][populate][addresses][fields][4]', 'country').set('populate[business_partner][populate][addresses][fields][5]', 'postal_code').set('populate[business_partner][populate][addresses][populate][emails][fields][0]', 'email_address').set('populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number').set('populate[business_partner][populate][addresses][populate][home_page_urls][fields][0]', 'website_url').set('populate[involved_parties][populate][business_partner][fields][0]', 'bp_full_name').set('populate[involved_parties][populate][business_partner][populate][addresses][populate][address_usages][fields][0]', 'address_usage').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][0]', 'city_name').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][1]', 'country').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][2]', 'house_number').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][3]', 'region').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][4]', 'street_name').set('populate[involved_parties][populate][business_partner][populate][addresses][fields][5]', 'postal_code').set('populate[involved_parties][populate][business_partner][populate][addresses][populate][emails][fields][0]', 'email_address').set('populate[involved_parties][populate][business_partner][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number').set('populate[involved_parties][populate][business_partner][populate][addresses][populate][phone_numbers][fields][1]', 'phone_number_type').set('populate[involved_parties][populate][business_partner][populate][customer][populate][partner_functions][fields][0]', 'partner_function').set('populate[follow_up_and_related_items][populate][activity_transaction][populate][business_partner][populate][customer][populate][partner_functions][fields][0]', 'partner_function').set('populate[follow_up_and_related_items][populate][activity_transaction][populate][business_partner][populate][customer][populate][partner_functions][populate][business_partner][fields][0]', 'bp_full_name').set('populate[business_partner_owner][fields][0]', 'bp_full_name').set('populate[business_partner_contact][fields][0]', 'bp_full_name').set('populate[business_partner_processor][fields][0]', 'bp_full_name').set('populate[business_partner_contact][populate][contact_persons][fields][0]', 'bp_company_id').set('populate[notes][populate]', '*').set('populate[opportunity_followups][populate]', '*');\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    }).pipe(map(response => {\n      const activityDetails = response?.data[0] || null;\n      this.activitySubject.next(activityDetails);\n      return response;\n    }));\n  }\n  getPartners(params) {\n    return this.http.get(`${CMS_APIContstant.PARTNERS}?populate[addresses][populate]=*&populate[address_usages][populate]=*`, {\n      params\n    }).pipe(map(response => (response?.data || []).map(item => {\n      const contact = item?.addresses?.[0];\n      const email = contact?.emails?.[0]?.email_address || '';\n      const mobile = (contact?.phone_numbers || []).filter(item => item.phone_number_type === '3').map(item => item.phone_number);\n      return {\n        bp_id: item?.bp_id || '',\n        bp_full_name: item?.bp_full_name || '',\n        email: email,\n        mobile: mobile\n      };\n    })));\n  }\n  getPartnersContact(params) {\n    return this.http.get(`${CMS_APIContstant.PARTNERS_CONTACTS}`, {\n      params\n    }).pipe(map(response => {\n      return (response?.data || []).map(item => {\n        const contact = item?.business_partner_person?.addresses?.[0];\n        const email = contact?.emails?.[0]?.email_address || '';\n        const mobile = (contact?.phone_numbers || []).filter(ph => ph.phone_number_type === '3').map(ph => ph.phone_number).join(', '); // Combine multiple numbers into one string\n        return {\n          bp_id: item?.business_partner_person?.bp_id || '',\n          bp_full_name: item?.business_partner_person?.bp_full_name || '',\n          email: email,\n          mobile: mobile\n        };\n      });\n    }));\n  }\n  getEmailwisePartner() {\n    const email = this.authservice.getUserEmail() ?? '';\n    let params = new HttpParams().set('filters[business_partner_person][addresses][emails][email_address][$containsi]', email).set('populate[business_partner_person][populate][addresses][populate]', '*');\n    return this.http.get(`${CMS_APIContstant.PARTNERS_CONTACTS}`, {\n      params\n    }).pipe(map(response => {\n      const firstItem = response?.data?.[0]?.business_partner_person;\n      return firstItem?.bp_id || null;\n    }));\n  }\n  getActivityCodeWise(params) {\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    });\n  }\n  getCrmAttachments(activityId) {\n    const params = new HttpParams().set('filters[activity_id][$eq]', activityId);\n    return this.http.get(`${CMS_APIContstant.CRM_ATTACHMENTS}`, {\n      params\n    });\n  }\n  createCrmAttachment(data) {\n    return this.http.post(`${CMS_APIContstant.CRM_ATTACHMENTS}`, {\n      data\n    });\n  }\n  getActivity(partnerId) {\n    let params = new HttpParams().set('filters[main_account_party_id][$eq]', partnerId).set('fields', 'subject,activity_id,start_date,end_date,createdAt,activity_status,phone_call_category,priority,document_type').set('populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number').set('populate[business_partner][populate][addresses][populate][phone_numbers][fields][1]', 'phone_number_type').set('populate[business_partner_contact][fields][0]', 'bp_full_name').set('populate[business_partner_organizer][fields][0]', 'bp_full_name').set('populate[notes][fields][0]', 'note').set('populate[notes][fields][1]', 'is_global_note');\n    return this.http.get(`${CMS_APIContstant.CRM_ACTIVITY}`, {\n      params\n    });\n  }\n  getTodayRange() {\n    const today = new Date();\n    const start = new Date(today.setHours(0, 0, 0, 0)).toISOString();\n    const end = new Date(today.setHours(23, 59, 59, 999)).toISOString();\n    return {\n      start,\n      end\n    };\n  }\n  getThisWeekRange() {\n    const now = new Date();\n    const startOfWeek = new Date(now);\n    const day = now.getDay();\n    const diffToMonday = day === 0 ? -6 : 1 - day;\n    startOfWeek.setDate(now.getDate() + diffToMonday);\n    startOfWeek.setHours(0, 0, 0, 0);\n    const endOfWeek = new Date(startOfWeek);\n    endOfWeek.setDate(startOfWeek.getDate() + 6);\n    endOfWeek.setHours(23, 59, 59, 999);\n    return {\n      start: startOfWeek.toISOString(),\n      end: endOfWeek.toISOString()\n    };\n  }\n  getThisMonthRange() {\n    const now = new Date();\n    const start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);\n    const end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);\n    return {\n      start: start.toISOString(),\n      end: end.toISOString()\n    };\n  }\n  static {\n    this.ɵfac = function ActivitiesService_Factory(t) {\n      return new (t || ActivitiesService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ActivitiesService,\n      factory: ActivitiesService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "map", "CMS_APIContstant", "ActivitiesService", "constructor", "http", "authservice", "activitySubject", "activity", "asObservable", "createNote", "data", "post", "CRM_NOTE", "createActivity", "CRM_ACTIVITY_PHONE_CALL_REGISTRATION", "createActivityTask", "CRM_ACTIVITY_TASK_REGISTRATION", "createFollowup", "CRM_ACTIVITY_FOLLOW_UP_REGISTRATION", "createOpportuniyFollowup", "CRM_OPPORTUNITY_ACTIVITY_FOLLOW_UP_REGISTRATION", "createInvolvedParty", "CRM_INVOLVED_PARTIES", "createRelatedItem", "CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS", "updateActivity", "Id", "put", "CRM_ACTIVITY", "updateNote", "updateActivityStatus", "deleteNote", "id", "delete", "deleteInvolvedParty", "deleteFollowupItem", "getActivityDropdownOptions", "type", "params", "set", "get", "CONFIG_DATA", "getActivities", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "toString", "undefined", "order", "getSalesCall", "filter", "today", "Date", "startOfDay", "setHours", "toISOString", "endOfDay", "now", "startOfWeek", "day", "getDay", "diffToMonday", "setDate", "getDate", "endOfWeek", "startISO", "endISO", "startOfMonth", "getFullYear", "getMonth", "endOfMonth", "email", "getUserEmail", "getTasks", "getActivityByID", "activityId", "pipe", "response", "activityDetails", "next", "getPartners", "PARTNERS", "item", "contact", "addresses", "emails", "email_address", "mobile", "phone_numbers", "phone_number_type", "phone_number", "bp_id", "bp_full_name", "getPartnersContact", "PARTNERS_CONTACTS", "business_partner_person", "ph", "join", "getEmail<PERSON><PERSON><PERSON><PERSON>", "firstItem", "getActivityCodeWise", "getCrmAttachments", "CRM_ATTACHMENTS", "createCrmAttachment", "getActivity", "partnerId", "getTodayRange", "start", "end", "getThisWeekRange", "getThisMonthRange", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\activities\\activities.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ActivitiesService {\r\n  public activitySubject = new BehaviorSubject<any>(null);\r\n  public activity = this.activitySubject.asObservable();\r\n\r\n  constructor(private http: HttpClient, private authservice: AuthService) {}\r\n\r\n  createNote(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  createActivity(data: any): Observable<any> {\r\n    return this.http.post(\r\n      `${CMS_APIContstant.CRM_ACTIVITY_PHONE_CALL_REGISTRATION}`,\r\n      data\r\n    );\r\n  }\r\n\r\n  createActivityTask(data: any): Observable<any> {\r\n    return this.http.post(\r\n      `${CMS_APIContstant.CRM_ACTIVITY_TASK_REGISTRATION}`,\r\n      data\r\n    );\r\n  }\r\n\r\n  createFollowup(data: any): Observable<any> {\r\n    return this.http.post(\r\n      `${CMS_APIContstant.CRM_ACTIVITY_FOLLOW_UP_REGISTRATION}`,\r\n      data\r\n    );\r\n  }\r\n\r\n  createOpportuniyFollowup(data: any): Observable<any> {\r\n    return this.http.post(\r\n      `${CMS_APIContstant.CRM_OPPORTUNITY_ACTIVITY_FOLLOW_UP_REGISTRATION}`,\r\n      data\r\n    );\r\n  }\r\n\r\n  createInvolvedParty(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_INVOLVED_PARTIES}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  createRelatedItem(data: any): Observable<any> {\r\n    return this.http.post(\r\n      `${CMS_APIContstant.CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS}`,\r\n      {\r\n        data,\r\n      }\r\n    );\r\n  }\r\n\r\n  updateActivity(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateNote(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {\r\n      data,\r\n    });\r\n  }\r\n\r\n  updateActivityStatus(Id: string, data: any): Observable<any> {\r\n    return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, { data });\r\n  }\r\n\r\n  deleteNote(id: string) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.CRM_NOTE}/${id}`);\r\n  }\r\n\r\n  deleteInvolvedParty(id: string) {\r\n    return this.http.delete<any>(\r\n      `${CMS_APIContstant.CRM_INVOLVED_PARTIES}/${id}`\r\n    );\r\n  }\r\n\r\n  deleteFollowupItem(id: string) {\r\n    return this.http.delete<any>(\r\n      `${CMS_APIContstant.CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS}/${id}`\r\n    );\r\n  }\r\n\r\n  getActivityDropdownOptions(type: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', type);\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });\r\n  }\r\n\r\n  getActivities(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set(\r\n        'fields',\r\n        'main_account_party_id,subject,activity_status,start_date,end_date,category'\r\n      );\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set(\r\n        'filters[$or][0][main_account_party_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\r\n      params = params.set('filters[$or][2][category][$containsi]', searchTerm);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });\r\n  }\r\n\r\n  getSalesCall(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string,\r\n    filter?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('filters[document_type][$eq]', '0002')\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set(\r\n        'fields',\r\n        'main_account_party_id,activity_id,subject,owner_party_id,brand,customer_group,ranking,activity_status,phone_call_category,customer_timezone,createdAt,updatedAt'\r\n      )\r\n      .set('populate[notes][fields][0]', 'note')\r\n      .set('populate[notes][fields][1]', 'is_global_note')\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][0]',\r\n        'region'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][1]',\r\n        'country'\r\n      )\r\n      .set('populate[business_partner][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_owner][fields][0]', 'bp_full_name');\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    } else {\r\n      // Default sort by updatedAt descending\r\n      params = params.set('sort', 'updatedAt:desc');\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set(\r\n        'filters[$or][0][main_account_party_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\r\n      params = params.set(\r\n        'filters[$or][2][phone_call_category][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][3][brand][$containsi]', searchTerm);\r\n      params = params.set(\r\n        'filters[$or][4][business_partner][bp_full_name][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set(\r\n        'filters[$or][5][ranking][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set(\r\n        'filters[$or][6][business_partner_owner][bp_full_name][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set(\r\n        'filters[$or][7][customer_group][$containsi]',\r\n        searchTerm\r\n      );\r\n      \r\n    }\r\n    if (filter) {\r\n      if (filter === 'MSCT') {\r\n        const today = new Date();\r\n        const startOfDay = new Date(today.setHours(0, 0, 0, 0)).toISOString();\r\n        const endOfDay = new Date(\r\n          today.setHours(23, 59, 59, 999)\r\n        ).toISOString();\r\n\r\n        params = params\r\n          .set('filters[createdAt][$gte]', startOfDay)\r\n          .set('filters[createdAt][$lte]', endOfDay);\r\n      } else if (filter === 'MSCTW') {\r\n        const now = new Date();\r\n\r\n        // Get the start of the week (Monday)\r\n        const startOfWeek = new Date(now);\r\n        const day = now.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6\r\n        const diffToMonday = day === 0 ? -6 : 1 - day;\r\n        startOfWeek.setDate(now.getDate() + diffToMonday);\r\n        startOfWeek.setHours(0, 0, 0, 0);\r\n\r\n        // Get the end of the week (Sunday)\r\n        const endOfWeek = new Date(startOfWeek);\r\n        endOfWeek.setDate(startOfWeek.getDate() + 6);\r\n        endOfWeek.setHours(23, 59, 59, 999);\r\n\r\n        const startISO = startOfWeek.toISOString();\r\n        const endISO = endOfWeek.toISOString();\r\n\r\n        params = params\r\n          .set('filters[createdAt][$gte]', startISO)\r\n          .set('filters[createdAt][$lte]', endISO);\r\n      } else if (filter === 'MSCTM') {\r\n        const now = new Date();\r\n\r\n        // Start of the month: 1st day at 00:00:00\r\n        const startOfMonth = new Date(\r\n          now.getFullYear(),\r\n          now.getMonth(),\r\n          1,\r\n          0,\r\n          0,\r\n          0,\r\n          0\r\n        );\r\n\r\n        // End of the month: last day at 23:59:59.999\r\n        const endOfMonth = new Date(\r\n          now.getFullYear(),\r\n          now.getMonth() + 1,\r\n          0,\r\n          23,\r\n          59,\r\n          59,\r\n          999\r\n        );\r\n\r\n        const startISO = startOfMonth.toISOString();\r\n        const endISO = endOfMonth.toISOString();\r\n\r\n        params = params\r\n          .set('filters[createdAt][$gte]', startISO)\r\n          .set('filters[createdAt][$lte]', endISO);\r\n      } else if (filter === 'MCSC') {\r\n        params = params.set('filters[activity_status][$eq]', '3');\r\n      } else if (filter === 'MSC') {\r\n        const email = this.authservice.getUserEmail();\r\n        if (email) {\r\n          params = params\r\n            .set(\r\n              `filters[$and][0][business_partner][customer][partner_functions][partner_function][$eq]`,\r\n              'YI'\r\n            )\r\n            .set(\r\n              `filters[$and][1][business_partner][customer][partner_functions][business_partner][addresses][emails][email_address][$containsi]`,\r\n              email\r\n            );\r\n        }\r\n      }\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });\r\n  }\r\n\r\n  getTasks(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string,\r\n    filter?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('filters[document_type][$eq]', '0006')\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString())\r\n      .set(\r\n        'fields',\r\n        'main_account_party_id,activity_id,subject,owner_party_id,activity_status,priority,processor_party_id,task_category,start_date,end_date'\r\n      )\r\n      .set('populate[notes][fields][0]', 'note')\r\n      .set('populate[notes][fields][1]', 'is_global_note')\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][0]',\r\n        'region'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][1]',\r\n        'country'\r\n      )\r\n      .set('populate[business_partner][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_contact][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_processor][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_owner][fields][0]', 'bp_full_name');\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    } else {\r\n      // Default sort by updatedAt descending\r\n      params = params.set('sort', 'updatedAt:desc');\r\n    }\r\n\r\n    if (searchTerm) {\r\n      params = params.set(\r\n        'filters[$or][0][main_account_party_id][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);\r\n      params = params.set(\r\n        'filters[$or][2][task_category][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set(\r\n        'filters[$or][3][business_partner][bp_full_name][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set(\r\n        'filters[$or][4][business_partner_contact][bp_full_name][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set(\r\n        'filters[$or][5][business_partner_owner][bp_full_name][$containsi]',\r\n        searchTerm\r\n      );\r\n      params = params.set(\r\n        'filters[$or][6][business_partner_processor][bp_full_name][$containsi]',\r\n        searchTerm\r\n      );\r\n    }\r\n    if (filter) {\r\n      if (filter === 'MTT') {\r\n        const today = new Date();\r\n        const startOfDay = new Date(today.setHours(0, 0, 0, 0)).toISOString();\r\n        const endOfDay = new Date(\r\n          today.setHours(23, 59, 59, 999)\r\n        ).toISOString();\r\n\r\n        params = params\r\n          .set('filters[createdAt][$gte]', startOfDay)\r\n          .set('filters[createdAt][$lte]', endOfDay);\r\n      } else if (filter === 'MTTW') {\r\n        const now = new Date();\r\n\r\n        // Get the start of the week (Monday)\r\n        const startOfWeek = new Date(now);\r\n        const day = now.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6\r\n        const diffToMonday = day === 0 ? -6 : 1 - day;\r\n        startOfWeek.setDate(now.getDate() + diffToMonday);\r\n        startOfWeek.setHours(0, 0, 0, 0);\r\n\r\n        // Get the end of the week (Sunday)\r\n        const endOfWeek = new Date(startOfWeek);\r\n        endOfWeek.setDate(startOfWeek.getDate() + 6);\r\n        endOfWeek.setHours(23, 59, 59, 999);\r\n\r\n        const startISO = startOfWeek.toISOString();\r\n        const endISO = endOfWeek.toISOString();\r\n\r\n        params = params\r\n          .set('filters[createdAt][$gte]', startISO)\r\n          .set('filters[createdAt][$lte]', endISO);\r\n      } else if (filter === 'MTTM') {\r\n        const now = new Date();\r\n\r\n        // Start of the month: 1st day at 00:00:00\r\n        const startOfMonth = new Date(\r\n          now.getFullYear(),\r\n          now.getMonth(),\r\n          1,\r\n          0,\r\n          0,\r\n          0,\r\n          0\r\n        );\r\n\r\n        // End of the month: last day at 23:59:59.999\r\n        const endOfMonth = new Date(\r\n          now.getFullYear(),\r\n          now.getMonth() + 1,\r\n          0,\r\n          23,\r\n          59,\r\n          59,\r\n          999\r\n        );\r\n\r\n        const startISO = startOfMonth.toISOString();\r\n        const endISO = endOfMonth.toISOString();\r\n\r\n        params = params\r\n          .set('filters[createdAt][$gte]', startISO)\r\n          .set('filters[createdAt][$lte]', endISO);\r\n      } else if (filter === 'MCT') {\r\n        params = params.set('filters[activity_status][$eq]', '3');\r\n      } else if (filter === 'MOT') {\r\n        params = params.set('filters[activity_status][$eq]', '1');\r\n      } else if (filter === 'MTST') {\r\n        // params = params.set('filters[activity_status][$eq]', '1');\r\n      } else if (filter === 'MT') {\r\n        const email = this.authservice.getUserEmail();\r\n        if (email) {\r\n          params = params\r\n            .set(\r\n              `filters[$and][0][business_partner][customer][partner_functions][partner_function][$eq]`,\r\n              'YI'\r\n            )\r\n            .set(\r\n              `filters[$and][1][business_partner][customer][partner_functions][business_partner][addresses][emails][email_address][$containsi]`,\r\n              email\r\n            );\r\n        }\r\n      }\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });\r\n  }\r\n\r\n  getActivityByID(activityId: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[activity_id][$eq]', activityId)\r\n      .set('populate[business_partner][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner][fields][1]', 'bp_id')\r\n      .set(\r\n        'populate[business_partner][populate][customer][populate][partner_functions][fields][0]',\r\n        'partner_function'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][0]',\r\n        'first_name'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][1]',\r\n        'last_name'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][address_usages][fields][0]',\r\n        'address_usage'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][0]',\r\n        'house_number'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][1]',\r\n        'street_name'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][2]',\r\n        'city_name'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][3]',\r\n        'region'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][4]',\r\n        'country'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][fields][5]',\r\n        'postal_code'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][emails][fields][0]',\r\n        'email_address'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]',\r\n        'phone_number'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][home_page_urls][fields][0]',\r\n        'website_url'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][fields][0]',\r\n        'bp_full_name'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][populate][address_usages][fields][0]',\r\n        'address_usage'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][fields][0]',\r\n        'city_name'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][fields][1]',\r\n        'country'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][fields][2]',\r\n        'house_number'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][fields][3]',\r\n        'region'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][fields][4]',\r\n        'street_name'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][fields][5]',\r\n        'postal_code'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][populate][emails][fields][0]',\r\n        'email_address'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][populate][phone_numbers][fields][0]',\r\n        'phone_number'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][addresses][populate][phone_numbers][fields][1]',\r\n        'phone_number_type'\r\n      )\r\n      .set(\r\n        'populate[involved_parties][populate][business_partner][populate][customer][populate][partner_functions][fields][0]',\r\n        'partner_function'\r\n      )\r\n      .set(\r\n        'populate[follow_up_and_related_items][populate][activity_transaction][populate][business_partner][populate][customer][populate][partner_functions][fields][0]',\r\n        'partner_function'\r\n      )\r\n      .set(\r\n        'populate[follow_up_and_related_items][populate][activity_transaction][populate][business_partner][populate][customer][populate][partner_functions][populate][business_partner][fields][0]',\r\n        'bp_full_name'\r\n      )\r\n      .set('populate[business_partner_owner][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_contact][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_processor][fields][0]', 'bp_full_name')\r\n      .set(\r\n        'populate[business_partner_contact][populate][contact_persons][fields][0]',\r\n        'bp_company_id'\r\n      )\r\n      .set('populate[notes][populate]', '*')\r\n      .set('populate[opportunity_followups][populate]', '*');\r\n\r\n    return this.http\r\n      .get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params })\r\n      .pipe(\r\n        map((response: any) => {\r\n          const activityDetails = response?.data[0] || null;\r\n          this.activitySubject.next(activityDetails);\r\n          return response;\r\n        })\r\n      );\r\n  }\r\n\r\n  getPartners(params: any) {\r\n    return this.http\r\n      .get<any>(\r\n        `${CMS_APIContstant.PARTNERS}?populate[addresses][populate]=*&populate[address_usages][populate]=*`,\r\n        { params }\r\n      )\r\n      .pipe(\r\n        map((response) =>\r\n          (response?.data || []).map((item: any) => {\r\n            const contact = item?.addresses?.[0];\r\n\r\n            const email = contact?.emails?.[0]?.email_address || '';\r\n            const mobile = (contact?.phone_numbers || [])\r\n              .filter((item: any) => item.phone_number_type === '3')\r\n              .map((item: any) => item.phone_number);\r\n\r\n            return {\r\n              bp_id: item?.bp_id || '',\r\n              bp_full_name: item?.bp_full_name || '',\r\n              email: email,\r\n              mobile: mobile,\r\n            };\r\n          })\r\n        )\r\n      );\r\n  }\r\n\r\n  getPartnersContact(params: any) {\r\n    return this.http\r\n      .get<any>(`${CMS_APIContstant.PARTNERS_CONTACTS}`, { params })\r\n      .pipe(\r\n        map((response) => {\r\n          return (response?.data || []).map((item: any) => {\r\n            const contact = item?.business_partner_person?.addresses?.[0];\r\n\r\n            const email = contact?.emails?.[0]?.email_address || '';\r\n            const mobile = (contact?.phone_numbers || [])\r\n              .filter((ph: any) => ph.phone_number_type === '3')\r\n              .map((ph: any) => ph.phone_number)\r\n              .join(', '); // Combine multiple numbers into one string\r\n\r\n            return {\r\n              bp_id: item?.business_partner_person?.bp_id || '',\r\n              bp_full_name: item?.business_partner_person?.bp_full_name || '',\r\n              email: email,\r\n              mobile: mobile,\r\n            };\r\n          });\r\n        })\r\n      );\r\n  }\r\n\r\n  getEmailwisePartner() {\r\n    const email = this.authservice.getUserEmail() ?? '';\r\n\r\n    let params = new HttpParams()\r\n      .set(\r\n        'filters[business_partner_person][addresses][emails][email_address][$containsi]',\r\n        email\r\n      )\r\n      .set(\r\n        'populate[business_partner_person][populate][addresses][populate]',\r\n        '*'\r\n      );\r\n\r\n    return this.http\r\n      .get<any>(`${CMS_APIContstant.PARTNERS_CONTACTS}`, { params })\r\n      .pipe(\r\n        map((response) => {\r\n          const firstItem = response?.data?.[0]?.business_partner_person;\r\n          return firstItem?.bp_id || null;\r\n        })\r\n      );\r\n  }\r\n\r\n  getActivityCodeWise(params: any) {\r\n    return this.http.get<any>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });\r\n  }\r\n\r\n  getCrmAttachments(activityId: string): Observable<any> {\r\n    const params = new HttpParams()\r\n      .set('filters[activity_id][$eq]', activityId)\r\n\r\n    return this.http.get<any>(`${CMS_APIContstant.CRM_ATTACHMENTS}`, { params });\r\n  }\r\n\r\n  createCrmAttachment(data: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.CRM_ATTACHMENTS}`, { data });\r\n  }\r\n\r\n  getActivity(partnerId: string): Observable<{ data: any[]; meta: any }> {\r\n    let params = new HttpParams()\r\n      .set('filters[main_account_party_id][$eq]', partnerId)\r\n      .set(\r\n        'fields',\r\n        'subject,activity_id,start_date,end_date,createdAt,activity_status,phone_call_category,priority,document_type'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]',\r\n        'phone_number'\r\n      )\r\n      .set(\r\n        'populate[business_partner][populate][addresses][populate][phone_numbers][fields][1]',\r\n        'phone_number_type'\r\n      )\r\n      .set('populate[business_partner_contact][fields][0]', 'bp_full_name')\r\n      .set('populate[business_partner_organizer][fields][0]', 'bp_full_name')\r\n      .set('populate[notes][fields][0]', 'note')\r\n      .set('populate[notes][fields][1]', 'is_global_note');\r\n\r\n    return this.http.get<{ data: any[]; meta: any }>(\r\n      `${CMS_APIContstant.CRM_ACTIVITY}`,\r\n      { params }\r\n    );\r\n  }\r\n\r\n  getTodayRange() {\r\n    const today = new Date();\r\n    const start = new Date(today.setHours(0, 0, 0, 0)).toISOString();\r\n    const end = new Date(today.setHours(23, 59, 59, 999)).toISOString();\r\n    return { start, end };\r\n  }\r\n\r\n  getThisWeekRange() {\r\n    const now = new Date();\r\n    const startOfWeek = new Date(now);\r\n    const day = now.getDay();\r\n    const diffToMonday = day === 0 ? -6 : 1 - day;\r\n    startOfWeek.setDate(now.getDate() + diffToMonday);\r\n    startOfWeek.setHours(0, 0, 0, 0);\r\n\r\n    const endOfWeek = new Date(startOfWeek);\r\n    endOfWeek.setDate(startOfWeek.getDate() + 6);\r\n    endOfWeek.setHours(23, 59, 59, 999);\r\n\r\n    return {\r\n      start: startOfWeek.toISOString(),\r\n      end: endOfWeek.toISOString(),\r\n    };\r\n  }\r\n\r\n  getThisMonthRange() {\r\n    const now = new Date();\r\n    const start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);\r\n    const end = new Date(\r\n      now.getFullYear(),\r\n      now.getMonth() + 1,\r\n      0,\r\n      23,\r\n      59,\r\n      59,\r\n      999\r\n    );\r\n    return {\r\n      start: start.toISOString(),\r\n      end: end.toISOString(),\r\n    };\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,gBAAgB,QAAQ,iCAAiC;;;;AAMlE,OAAM,MAAOC,iBAAiB;EAI5BC,YAAoBC,IAAgB,EAAUC,WAAwB;IAAlD,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,WAAW,GAAXA,WAAW;IAHlD,KAAAC,eAAe,GAAG,IAAIP,eAAe,CAAM,IAAI,CAAC;IAChD,KAAAQ,QAAQ,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;EAEoB;EAEzEC,UAAUA,CAACC,IAAS;IAClB,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,GAAGV,gBAAgB,CAACW,QAAQ,EAAE,EAAE;MACpDF;KACD,CAAC;EACJ;EAEAG,cAAcA,CAACH,IAAS;IACtB,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CACnB,GAAGV,gBAAgB,CAACa,oCAAoC,EAAE,EAC1DJ,IAAI,CACL;EACH;EAEAK,kBAAkBA,CAACL,IAAS;IAC1B,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CACnB,GAAGV,gBAAgB,CAACe,8BAA8B,EAAE,EACpDN,IAAI,CACL;EACH;EAEAO,cAAcA,CAACP,IAAS;IACtB,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CACnB,GAAGV,gBAAgB,CAACiB,mCAAmC,EAAE,EACzDR,IAAI,CACL;EACH;EAEAS,wBAAwBA,CAACT,IAAS;IAChC,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CACnB,GAAGV,gBAAgB,CAACmB,+CAA+C,EAAE,EACrEV,IAAI,CACL;EACH;EAEAW,mBAAmBA,CAACX,IAAS;IAC3B,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,GAAGV,gBAAgB,CAACqB,oBAAoB,EAAE,EAAE;MAChEZ;KACD,CAAC;EACJ;EAEAa,iBAAiBA,CAACb,IAAS;IACzB,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CACnB,GAAGV,gBAAgB,CAACuB,mCAAmC,EAAE,EACzD;MACEd;KACD,CACF;EACH;EAEAe,cAAcA,CAACC,EAAU,EAAEhB,IAAS;IAClC,OAAO,IAAI,CAACN,IAAI,CAACuB,GAAG,CAAC,GAAG1B,gBAAgB,CAAC2B,YAAY,IAAIF,EAAE,EAAE,EAAE;MAC7DhB;KACD,CAAC;EACJ;EAEAmB,UAAUA,CAACH,EAAU,EAAEhB,IAAS;IAC9B,OAAO,IAAI,CAACN,IAAI,CAACuB,GAAG,CAAC,GAAG1B,gBAAgB,CAACW,QAAQ,IAAIc,EAAE,EAAE,EAAE;MACzDhB;KACD,CAAC;EACJ;EAEAoB,oBAAoBA,CAACJ,EAAU,EAAEhB,IAAS;IACxC,OAAO,IAAI,CAACN,IAAI,CAACuB,GAAG,CAAC,GAAG1B,gBAAgB,CAAC2B,YAAY,IAAIF,EAAE,EAAE,EAAE;MAAEhB;IAAI,CAAE,CAAC;EAC1E;EAEAqB,UAAUA,CAACC,EAAU;IACnB,OAAO,IAAI,CAAC5B,IAAI,CAAC6B,MAAM,CAAM,GAAGhC,gBAAgB,CAACW,QAAQ,IAAIoB,EAAE,EAAE,CAAC;EACpE;EAEAE,mBAAmBA,CAACF,EAAU;IAC5B,OAAO,IAAI,CAAC5B,IAAI,CAAC6B,MAAM,CACrB,GAAGhC,gBAAgB,CAACqB,oBAAoB,IAAIU,EAAE,EAAE,CACjD;EACH;EAEAG,kBAAkBA,CAACH,EAAU;IAC3B,OAAO,IAAI,CAAC5B,IAAI,CAAC6B,MAAM,CACrB,GAAGhC,gBAAgB,CAACuB,mCAAmC,IAAIQ,EAAE,EAAE,CAChE;EACH;EAEAI,0BAA0BA,CAACC,IAAY;IACrC,MAAMC,MAAM,GAAG,IAAIxC,UAAU,EAAE,CAC5ByC,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAEF,IAAI,CAAC;IAElC,OAAO,IAAI,CAACjC,IAAI,CAACoC,GAAG,CAAM,GAAGvC,gBAAgB,CAACwC,WAAW,EAAE,EAAE;MAAEH;IAAM,CAAE,CAAC;EAC1E;EAEAI,aAAaA,CACXC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIT,MAAM,GAAG,IAAIxC,UAAU,EAAE,CAC1ByC,GAAG,CAAC,kBAAkB,EAAEI,IAAI,CAACK,QAAQ,EAAE,CAAC,CACxCT,GAAG,CAAC,sBAAsB,EAAEK,QAAQ,CAACI,QAAQ,EAAE,CAAC,CAChDT,GAAG,CACF,QAAQ,EACR,4EAA4E,CAC7E;IAEH,IAAIM,SAAS,IAAIC,SAAS,KAAKG,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGJ,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CR,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGM,SAAS,IAAIK,KAAK,EAAE,CAAC;IACtD;IAEA,IAAIH,UAAU,EAAE;MACdT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,oDAAoD,EACpDQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,sCAAsC,EAAEQ,UAAU,CAAC;MACvET,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,uCAAuC,EAAEQ,UAAU,CAAC;IAC1E;IAEA,OAAO,IAAI,CAAC3C,IAAI,CAACoC,GAAG,CAAQ,GAAGvC,gBAAgB,CAAC2B,YAAY,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC;EAC7E;EAEAa,YAAYA,CACVR,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB,EACnBK,MAAe;IAEf,IAAId,MAAM,GAAG,IAAIxC,UAAU,EAAE,CAC1ByC,GAAG,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAC1CA,GAAG,CAAC,kBAAkB,EAAEI,IAAI,CAACK,QAAQ,EAAE,CAAC,CACxCT,GAAG,CAAC,sBAAsB,EAAEK,QAAQ,CAACI,QAAQ,EAAE,CAAC,CAChDT,GAAG,CACF,QAAQ,EACR,iKAAiK,CAClK,CACAA,GAAG,CAAC,4BAA4B,EAAE,MAAM,CAAC,CACzCA,GAAG,CAAC,4BAA4B,EAAE,gBAAgB,CAAC,CACnDA,GAAG,CACF,4DAA4D,EAC5D,QAAQ,CACT,CACAA,GAAG,CACF,4DAA4D,EAC5D,SAAS,CACV,CACAA,GAAG,CAAC,uCAAuC,EAAE,cAAc,CAAC,CAC5DA,GAAG,CAAC,6CAA6C,EAAE,cAAc,CAAC;IAErE,IAAIM,SAAS,IAAIC,SAAS,KAAKG,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGJ,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CR,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGM,SAAS,IAAIK,KAAK,EAAE,CAAC;IACtD,CAAC,MAAM;MACL;MACAZ,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC;IAC/C;IAEA,IAAIQ,UAAU,EAAE;MACdT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,oDAAoD,EACpDQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,sCAAsC,EAAEQ,UAAU,CAAC;MACvET,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,kDAAkD,EAClDQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,oCAAoC,EAAEQ,UAAU,CAAC;MACrET,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,6DAA6D,EAC7DQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,sCAAsC,EACtCQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,mEAAmE,EACnEQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,6CAA6C,EAC7CQ,UAAU,CACX;IAEH;IACA,IAAIK,MAAM,EAAE;MACV,IAAIA,MAAM,KAAK,MAAM,EAAE;QACrB,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;QACxB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAACD,KAAK,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;QACrE,MAAMC,QAAQ,GAAG,IAAIJ,IAAI,CACvBD,KAAK,CAACG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAChC,CAACC,WAAW,EAAE;QAEfnB,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0BAA0B,EAAEgB,UAAU,CAAC,CAC3ChB,GAAG,CAAC,0BAA0B,EAAEmB,QAAQ,CAAC;MAC9C,CAAC,MAAM,IAAIN,MAAM,KAAK,OAAO,EAAE;QAC7B,MAAMO,GAAG,GAAG,IAAIL,IAAI,EAAE;QAEtB;QACA,MAAMM,WAAW,GAAG,IAAIN,IAAI,CAACK,GAAG,CAAC;QACjC,MAAME,GAAG,GAAGF,GAAG,CAACG,MAAM,EAAE,CAAC,CAAC;QAC1B,MAAMC,YAAY,GAAGF,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGA,GAAG;QAC7CD,WAAW,CAACI,OAAO,CAACL,GAAG,CAACM,OAAO,EAAE,GAAGF,YAAY,CAAC;QACjDH,WAAW,CAACJ,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAEhC;QACA,MAAMU,SAAS,GAAG,IAAIZ,IAAI,CAACM,WAAW,CAAC;QACvCM,SAAS,CAACF,OAAO,CAACJ,WAAW,CAACK,OAAO,EAAE,GAAG,CAAC,CAAC;QAC5CC,SAAS,CAACV,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;QAEnC,MAAMW,QAAQ,GAAGP,WAAW,CAACH,WAAW,EAAE;QAC1C,MAAMW,MAAM,GAAGF,SAAS,CAACT,WAAW,EAAE;QAEtCnB,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0BAA0B,EAAE4B,QAAQ,CAAC,CACzC5B,GAAG,CAAC,0BAA0B,EAAE6B,MAAM,CAAC;MAC5C,CAAC,MAAM,IAAIhB,MAAM,KAAK,OAAO,EAAE;QAC7B,MAAMO,GAAG,GAAG,IAAIL,IAAI,EAAE;QAEtB;QACA,MAAMe,YAAY,GAAG,IAAIf,IAAI,CAC3BK,GAAG,CAACW,WAAW,EAAE,EACjBX,GAAG,CAACY,QAAQ,EAAE,EACd,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,CACF;QAED;QACA,MAAMC,UAAU,GAAG,IAAIlB,IAAI,CACzBK,GAAG,CAACW,WAAW,EAAE,EACjBX,GAAG,CAACY,QAAQ,EAAE,GAAG,CAAC,EAClB,CAAC,EACD,EAAE,EACF,EAAE,EACF,EAAE,EACF,GAAG,CACJ;QAED,MAAMJ,QAAQ,GAAGE,YAAY,CAACZ,WAAW,EAAE;QAC3C,MAAMW,MAAM,GAAGI,UAAU,CAACf,WAAW,EAAE;QAEvCnB,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0BAA0B,EAAE4B,QAAQ,CAAC,CACzC5B,GAAG,CAAC,0BAA0B,EAAE6B,MAAM,CAAC;MAC5C,CAAC,MAAM,IAAIhB,MAAM,KAAK,MAAM,EAAE;QAC5Bd,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,+BAA+B,EAAE,GAAG,CAAC;MAC3D,CAAC,MAAM,IAAIa,MAAM,KAAK,KAAK,EAAE;QAC3B,MAAMqB,KAAK,GAAG,IAAI,CAACpE,WAAW,CAACqE,YAAY,EAAE;QAC7C,IAAID,KAAK,EAAE;UACTnC,MAAM,GAAGA,MAAM,CACZC,GAAG,CACF,wFAAwF,EACxF,IAAI,CACL,CACAA,GAAG,CACF,iIAAiI,EACjIkC,KAAK,CACN;QACL;MACF;IACF;IAEA,OAAO,IAAI,CAACrE,IAAI,CAACoC,GAAG,CAAQ,GAAGvC,gBAAgB,CAAC2B,YAAY,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC;EAC7E;EAEAqC,QAAQA,CACNhC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB,EACnBK,MAAe;IAEf,IAAId,MAAM,GAAG,IAAIxC,UAAU,EAAE,CAC1ByC,GAAG,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAC1CA,GAAG,CAAC,kBAAkB,EAAEI,IAAI,CAACK,QAAQ,EAAE,CAAC,CACxCT,GAAG,CAAC,sBAAsB,EAAEK,QAAQ,CAACI,QAAQ,EAAE,CAAC,CAChDT,GAAG,CACF,QAAQ,EACR,wIAAwI,CACzI,CACAA,GAAG,CAAC,4BAA4B,EAAE,MAAM,CAAC,CACzCA,GAAG,CAAC,4BAA4B,EAAE,gBAAgB,CAAC,CACnDA,GAAG,CACF,4DAA4D,EAC5D,QAAQ,CACT,CACAA,GAAG,CACF,4DAA4D,EAC5D,SAAS,CACV,CACAA,GAAG,CAAC,uCAAuC,EAAE,cAAc,CAAC,CAC5DA,GAAG,CAAC,+CAA+C,EAAE,cAAc,CAAC,CACpEA,GAAG,CAAC,iDAAiD,EAAE,cAAc,CAAC,CACtEA,GAAG,CAAC,6CAA6C,EAAE,cAAc,CAAC;IAErE,IAAIM,SAAS,IAAIC,SAAS,KAAKG,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGJ,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CR,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGM,SAAS,IAAIK,KAAK,EAAE,CAAC;IACtD,CAAC,MAAM;MACL;MACAZ,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC;IAC/C;IAEA,IAAIQ,UAAU,EAAE;MACdT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,oDAAoD,EACpDQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,sCAAsC,EAAEQ,UAAU,CAAC;MACvET,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,4CAA4C,EAC5CQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,6DAA6D,EAC7DQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,qEAAqE,EACrEQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,mEAAmE,EACnEQ,UAAU,CACX;MACDT,MAAM,GAAGA,MAAM,CAACC,GAAG,CACjB,uEAAuE,EACvEQ,UAAU,CACX;IACH;IACA,IAAIK,MAAM,EAAE;MACV,IAAIA,MAAM,KAAK,KAAK,EAAE;QACpB,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;QACxB,MAAMC,UAAU,GAAG,IAAID,IAAI,CAACD,KAAK,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;QACrE,MAAMC,QAAQ,GAAG,IAAIJ,IAAI,CACvBD,KAAK,CAACG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAChC,CAACC,WAAW,EAAE;QAEfnB,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0BAA0B,EAAEgB,UAAU,CAAC,CAC3ChB,GAAG,CAAC,0BAA0B,EAAEmB,QAAQ,CAAC;MAC9C,CAAC,MAAM,IAAIN,MAAM,KAAK,MAAM,EAAE;QAC5B,MAAMO,GAAG,GAAG,IAAIL,IAAI,EAAE;QAEtB;QACA,MAAMM,WAAW,GAAG,IAAIN,IAAI,CAACK,GAAG,CAAC;QACjC,MAAME,GAAG,GAAGF,GAAG,CAACG,MAAM,EAAE,CAAC,CAAC;QAC1B,MAAMC,YAAY,GAAGF,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGA,GAAG;QAC7CD,WAAW,CAACI,OAAO,CAACL,GAAG,CAACM,OAAO,EAAE,GAAGF,YAAY,CAAC;QACjDH,WAAW,CAACJ,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAEhC;QACA,MAAMU,SAAS,GAAG,IAAIZ,IAAI,CAACM,WAAW,CAAC;QACvCM,SAAS,CAACF,OAAO,CAACJ,WAAW,CAACK,OAAO,EAAE,GAAG,CAAC,CAAC;QAC5CC,SAAS,CAACV,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;QAEnC,MAAMW,QAAQ,GAAGP,WAAW,CAACH,WAAW,EAAE;QAC1C,MAAMW,MAAM,GAAGF,SAAS,CAACT,WAAW,EAAE;QAEtCnB,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0BAA0B,EAAE4B,QAAQ,CAAC,CACzC5B,GAAG,CAAC,0BAA0B,EAAE6B,MAAM,CAAC;MAC5C,CAAC,MAAM,IAAIhB,MAAM,KAAK,MAAM,EAAE;QAC5B,MAAMO,GAAG,GAAG,IAAIL,IAAI,EAAE;QAEtB;QACA,MAAMe,YAAY,GAAG,IAAIf,IAAI,CAC3BK,GAAG,CAACW,WAAW,EAAE,EACjBX,GAAG,CAACY,QAAQ,EAAE,EACd,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,CACF;QAED;QACA,MAAMC,UAAU,GAAG,IAAIlB,IAAI,CACzBK,GAAG,CAACW,WAAW,EAAE,EACjBX,GAAG,CAACY,QAAQ,EAAE,GAAG,CAAC,EAClB,CAAC,EACD,EAAE,EACF,EAAE,EACF,EAAE,EACF,GAAG,CACJ;QAED,MAAMJ,QAAQ,GAAGE,YAAY,CAACZ,WAAW,EAAE;QAC3C,MAAMW,MAAM,GAAGI,UAAU,CAACf,WAAW,EAAE;QAEvCnB,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0BAA0B,EAAE4B,QAAQ,CAAC,CACzC5B,GAAG,CAAC,0BAA0B,EAAE6B,MAAM,CAAC;MAC5C,CAAC,MAAM,IAAIhB,MAAM,KAAK,KAAK,EAAE;QAC3Bd,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,+BAA+B,EAAE,GAAG,CAAC;MAC3D,CAAC,MAAM,IAAIa,MAAM,KAAK,KAAK,EAAE;QAC3Bd,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,+BAA+B,EAAE,GAAG,CAAC;MAC3D,CAAC,MAAM,IAAIa,MAAM,KAAK,MAAM,EAAE;QAC5B;MAAA,CACD,MAAM,IAAIA,MAAM,KAAK,IAAI,EAAE;QAC1B,MAAMqB,KAAK,GAAG,IAAI,CAACpE,WAAW,CAACqE,YAAY,EAAE;QAC7C,IAAID,KAAK,EAAE;UACTnC,MAAM,GAAGA,MAAM,CACZC,GAAG,CACF,wFAAwF,EACxF,IAAI,CACL,CACAA,GAAG,CACF,iIAAiI,EACjIkC,KAAK,CACN;QACL;MACF;IACF;IAEA,OAAO,IAAI,CAACrE,IAAI,CAACoC,GAAG,CAAQ,GAAGvC,gBAAgB,CAAC2B,YAAY,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC;EAC7E;EAEAsC,eAAeA,CAACC,UAAkB;IAChC,MAAMvC,MAAM,GAAG,IAAIxC,UAAU,EAAE,CAC5ByC,GAAG,CAAC,2BAA2B,EAAEsC,UAAU,CAAC,CAC5CtC,GAAG,CAAC,uCAAuC,EAAE,cAAc,CAAC,CAC5DA,GAAG,CAAC,uCAAuC,EAAE,OAAO,CAAC,CACrDA,GAAG,CACF,wFAAwF,EACxF,kBAAkB,CACnB,CACAA,GAAG,CACF,uGAAuG,EACvG,YAAY,CACb,CACAA,GAAG,CACF,uGAAuG,EACvG,WAAW,CACZ,CACAA,GAAG,CACF,sFAAsF,EACtF,eAAe,CAChB,CACAA,GAAG,CACF,4DAA4D,EAC5D,cAAc,CACf,CACAA,GAAG,CACF,4DAA4D,EAC5D,aAAa,CACd,CACAA,GAAG,CACF,4DAA4D,EAC5D,WAAW,CACZ,CACAA,GAAG,CACF,4DAA4D,EAC5D,QAAQ,CACT,CACAA,GAAG,CACF,4DAA4D,EAC5D,SAAS,CACV,CACAA,GAAG,CACF,4DAA4D,EAC5D,aAAa,CACd,CACAA,GAAG,CACF,8EAA8E,EAC9E,eAAe,CAChB,CACAA,GAAG,CACF,qFAAqF,EACrF,cAAc,CACf,CACAA,GAAG,CACF,sFAAsF,EACtF,aAAa,CACd,CACAA,GAAG,CACF,mEAAmE,EACnE,cAAc,CACf,CACAA,GAAG,CACF,kHAAkH,EAClH,eAAe,CAChB,CACAA,GAAG,CACF,wFAAwF,EACxF,WAAW,CACZ,CACAA,GAAG,CACF,wFAAwF,EACxF,SAAS,CACV,CACAA,GAAG,CACF,wFAAwF,EACxF,cAAc,CACf,CACAA,GAAG,CACF,wFAAwF,EACxF,QAAQ,CACT,CACAA,GAAG,CACF,wFAAwF,EACxF,aAAa,CACd,CACAA,GAAG,CACF,wFAAwF,EACxF,aAAa,CACd,CACAA,GAAG,CACF,0GAA0G,EAC1G,eAAe,CAChB,CACAA,GAAG,CACF,iHAAiH,EACjH,cAAc,CACf,CACAA,GAAG,CACF,iHAAiH,EACjH,mBAAmB,CACpB,CACAA,GAAG,CACF,oHAAoH,EACpH,kBAAkB,CACnB,CACAA,GAAG,CACF,+JAA+J,EAC/J,kBAAkB,CACnB,CACAA,GAAG,CACF,2LAA2L,EAC3L,cAAc,CACf,CACAA,GAAG,CAAC,6CAA6C,EAAE,cAAc,CAAC,CAClEA,GAAG,CAAC,+CAA+C,EAAE,cAAc,CAAC,CACpEA,GAAG,CAAC,iDAAiD,EAAE,cAAc,CAAC,CACtEA,GAAG,CACF,0EAA0E,EAC1E,eAAe,CAChB,CACAA,GAAG,CAAC,2BAA2B,EAAE,GAAG,CAAC,CACrCA,GAAG,CAAC,2CAA2C,EAAE,GAAG,CAAC;IAExD,OAAO,IAAI,CAACnC,IAAI,CACboC,GAAG,CAAQ,GAAGvC,gBAAgB,CAAC2B,YAAY,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC,CAC1DwC,IAAI,CACH9E,GAAG,CAAE+E,QAAa,IAAI;MACpB,MAAMC,eAAe,GAAGD,QAAQ,EAAErE,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;MACjD,IAAI,CAACJ,eAAe,CAAC2E,IAAI,CAACD,eAAe,CAAC;MAC1C,OAAOD,QAAQ;IACjB,CAAC,CAAC,CACH;EACL;EAEAG,WAAWA,CAAC5C,MAAW;IACrB,OAAO,IAAI,CAAClC,IAAI,CACboC,GAAG,CACF,GAAGvC,gBAAgB,CAACkF,QAAQ,uEAAuE,EACnG;MAAE7C;IAAM,CAAE,CACX,CACAwC,IAAI,CACH9E,GAAG,CAAE+E,QAAQ,IACX,CAACA,QAAQ,EAAErE,IAAI,IAAI,EAAE,EAAEV,GAAG,CAAEoF,IAAS,IAAI;MACvC,MAAMC,OAAO,GAAGD,IAAI,EAAEE,SAAS,GAAG,CAAC,CAAC;MAEpC,MAAMb,KAAK,GAAGY,OAAO,EAAEE,MAAM,GAAG,CAAC,CAAC,EAAEC,aAAa,IAAI,EAAE;MACvD,MAAMC,MAAM,GAAG,CAACJ,OAAO,EAAEK,aAAa,IAAI,EAAE,EACzCtC,MAAM,CAAEgC,IAAS,IAAKA,IAAI,CAACO,iBAAiB,KAAK,GAAG,CAAC,CACrD3F,GAAG,CAAEoF,IAAS,IAAKA,IAAI,CAACQ,YAAY,CAAC;MAExC,OAAO;QACLC,KAAK,EAAET,IAAI,EAAES,KAAK,IAAI,EAAE;QACxBC,YAAY,EAAEV,IAAI,EAAEU,YAAY,IAAI,EAAE;QACtCrB,KAAK,EAAEA,KAAK;QACZgB,MAAM,EAAEA;OACT;IACH,CAAC,CAAC,CACH,CACF;EACL;EAEAM,kBAAkBA,CAACzD,MAAW;IAC5B,OAAO,IAAI,CAAClC,IAAI,CACboC,GAAG,CAAM,GAAGvC,gBAAgB,CAAC+F,iBAAiB,EAAE,EAAE;MAAE1D;IAAM,CAAE,CAAC,CAC7DwC,IAAI,CACH9E,GAAG,CAAE+E,QAAQ,IAAI;MACf,OAAO,CAACA,QAAQ,EAAErE,IAAI,IAAI,EAAE,EAAEV,GAAG,CAAEoF,IAAS,IAAI;QAC9C,MAAMC,OAAO,GAAGD,IAAI,EAAEa,uBAAuB,EAAEX,SAAS,GAAG,CAAC,CAAC;QAE7D,MAAMb,KAAK,GAAGY,OAAO,EAAEE,MAAM,GAAG,CAAC,CAAC,EAAEC,aAAa,IAAI,EAAE;QACvD,MAAMC,MAAM,GAAG,CAACJ,OAAO,EAAEK,aAAa,IAAI,EAAE,EACzCtC,MAAM,CAAE8C,EAAO,IAAKA,EAAE,CAACP,iBAAiB,KAAK,GAAG,CAAC,CACjD3F,GAAG,CAAEkG,EAAO,IAAKA,EAAE,CAACN,YAAY,CAAC,CACjCO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAEf,OAAO;UACLN,KAAK,EAAET,IAAI,EAAEa,uBAAuB,EAAEJ,KAAK,IAAI,EAAE;UACjDC,YAAY,EAAEV,IAAI,EAAEa,uBAAuB,EAAEH,YAAY,IAAI,EAAE;UAC/DrB,KAAK,EAAEA,KAAK;UACZgB,MAAM,EAAEA;SACT;MACH,CAAC,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEAW,mBAAmBA,CAAA;IACjB,MAAM3B,KAAK,GAAG,IAAI,CAACpE,WAAW,CAACqE,YAAY,EAAE,IAAI,EAAE;IAEnD,IAAIpC,MAAM,GAAG,IAAIxC,UAAU,EAAE,CAC1ByC,GAAG,CACF,gFAAgF,EAChFkC,KAAK,CACN,CACAlC,GAAG,CACF,kEAAkE,EAClE,GAAG,CACJ;IAEH,OAAO,IAAI,CAACnC,IAAI,CACboC,GAAG,CAAM,GAAGvC,gBAAgB,CAAC+F,iBAAiB,EAAE,EAAE;MAAE1D;IAAM,CAAE,CAAC,CAC7DwC,IAAI,CACH9E,GAAG,CAAE+E,QAAQ,IAAI;MACf,MAAMsB,SAAS,GAAGtB,QAAQ,EAAErE,IAAI,GAAG,CAAC,CAAC,EAAEuF,uBAAuB;MAC9D,OAAOI,SAAS,EAAER,KAAK,IAAI,IAAI;IACjC,CAAC,CAAC,CACH;EACL;EAEAS,mBAAmBA,CAAChE,MAAW;IAC7B,OAAO,IAAI,CAAClC,IAAI,CAACoC,GAAG,CAAM,GAAGvC,gBAAgB,CAAC2B,YAAY,EAAE,EAAE;MAAEU;IAAM,CAAE,CAAC;EAC3E;EAEAiE,iBAAiBA,CAAC1B,UAAkB;IAClC,MAAMvC,MAAM,GAAG,IAAIxC,UAAU,EAAE,CAC5ByC,GAAG,CAAC,2BAA2B,EAAEsC,UAAU,CAAC;IAE/C,OAAO,IAAI,CAACzE,IAAI,CAACoC,GAAG,CAAM,GAAGvC,gBAAgB,CAACuG,eAAe,EAAE,EAAE;MAAElE;IAAM,CAAE,CAAC;EAC9E;EAEAmE,mBAAmBA,CAAC/F,IAAS;IAC3B,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,GAAGV,gBAAgB,CAACuG,eAAe,EAAE,EAAE;MAAE9F;IAAI,CAAE,CAAC;EACxE;EAEAgG,WAAWA,CAACC,SAAiB;IAC3B,IAAIrE,MAAM,GAAG,IAAIxC,UAAU,EAAE,CAC1ByC,GAAG,CAAC,qCAAqC,EAAEoE,SAAS,CAAC,CACrDpE,GAAG,CACF,QAAQ,EACR,8GAA8G,CAC/G,CACAA,GAAG,CACF,qFAAqF,EACrF,cAAc,CACf,CACAA,GAAG,CACF,qFAAqF,EACrF,mBAAmB,CACpB,CACAA,GAAG,CAAC,+CAA+C,EAAE,cAAc,CAAC,CACpEA,GAAG,CAAC,iDAAiD,EAAE,cAAc,CAAC,CACtEA,GAAG,CAAC,4BAA4B,EAAE,MAAM,CAAC,CACzCA,GAAG,CAAC,4BAA4B,EAAE,gBAAgB,CAAC;IAEtD,OAAO,IAAI,CAACnC,IAAI,CAACoC,GAAG,CAClB,GAAGvC,gBAAgB,CAAC2B,YAAY,EAAE,EAClC;MAAEU;IAAM,CAAE,CACX;EACH;EAEAsE,aAAaA,CAAA;IACX,MAAMvD,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAMuD,KAAK,GAAG,IAAIvD,IAAI,CAACD,KAAK,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;IAChE,MAAMqD,GAAG,GAAG,IAAIxD,IAAI,CAACD,KAAK,CAACG,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAACC,WAAW,EAAE;IACnE,OAAO;MAAEoD,KAAK;MAAEC;IAAG,CAAE;EACvB;EAEAC,gBAAgBA,CAAA;IACd,MAAMpD,GAAG,GAAG,IAAIL,IAAI,EAAE;IACtB,MAAMM,WAAW,GAAG,IAAIN,IAAI,CAACK,GAAG,CAAC;IACjC,MAAME,GAAG,GAAGF,GAAG,CAACG,MAAM,EAAE;IACxB,MAAMC,YAAY,GAAGF,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGA,GAAG;IAC7CD,WAAW,CAACI,OAAO,CAACL,GAAG,CAACM,OAAO,EAAE,GAAGF,YAAY,CAAC;IACjDH,WAAW,CAACJ,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEhC,MAAMU,SAAS,GAAG,IAAIZ,IAAI,CAACM,WAAW,CAAC;IACvCM,SAAS,CAACF,OAAO,CAACJ,WAAW,CAACK,OAAO,EAAE,GAAG,CAAC,CAAC;IAC5CC,SAAS,CAACV,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IAEnC,OAAO;MACLqD,KAAK,EAAEjD,WAAW,CAACH,WAAW,EAAE;MAChCqD,GAAG,EAAE5C,SAAS,CAACT,WAAW;KAC3B;EACH;EAEAuD,iBAAiBA,CAAA;IACf,MAAMrD,GAAG,GAAG,IAAIL,IAAI,EAAE;IACtB,MAAMuD,KAAK,GAAG,IAAIvD,IAAI,CAACK,GAAG,CAACW,WAAW,EAAE,EAAEX,GAAG,CAACY,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxE,MAAMuC,GAAG,GAAG,IAAIxD,IAAI,CAClBK,GAAG,CAACW,WAAW,EAAE,EACjBX,GAAG,CAACY,QAAQ,EAAE,GAAG,CAAC,EAClB,CAAC,EACD,EAAE,EACF,EAAE,EACF,EAAE,EACF,GAAG,CACJ;IACD,OAAO;MACLsC,KAAK,EAAEA,KAAK,CAACpD,WAAW,EAAE;MAC1BqD,GAAG,EAAEA,GAAG,CAACrD,WAAW;KACrB;EACH;;;uBAltBWvD,iBAAiB,EAAA+G,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAjBpH,iBAAiB;MAAAqH,OAAA,EAAjBrH,iBAAiB,CAAAsH,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}